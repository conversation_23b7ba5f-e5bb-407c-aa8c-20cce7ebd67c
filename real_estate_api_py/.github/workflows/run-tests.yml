# This workflow will test the python package against <PERSON><PERSON>'s Mock Servers
# Tests will run automatically on a push to `main` or a pull request event

name: Run Pytest

on:
  push:
    branches:
      - main
  pull_request: {}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      #----------------------------------------------
      #  -----  checkout & setup python  -----
      #----------------------------------------------
      - name: checkout repository
        uses: actions/checkout@v4
      - name: setup python
        uses: actions/setup-python@v5
        id: setup-python
        with:
          python-version: "3.8"
      #----------------------------------------------
      #  -----  install & configure poetry  -----
      #----------------------------------------------
      - name: install poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.8.5
          virtualenvs-create: true
          virtualenvs-in-project: true
          virtualenvs-path: .venv
          installer-parallel: true
      #---------------------------------------------------
      #  -----  load cached venv if cache exists -----
      #---------------------------------------------------
      - name: load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v4
        with:
          path: .venv
          key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}
      #---------------------------------------------------------------
      #  -----  install dependencies if cache does not exist -----
      #---------------------------------------------------------------
      - name: install dependencies
        if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
        run: poetry install --no-interaction --no-root
      #-----------------------------------------------------------
      #  -----  install your root project, if required -----
      #-----------------------------------------------------------
      - name: install project
        run: poetry install --no-interaction
      #------------------------------
      #  -----  run pytest -----
      #------------------------------
      - name: run pytest
        run: |
          source .venv/bin/activate
          pytest -s
      #------------------------------
      #  -----  run mypy -----
      #------------------------------
      - name: run mypy
        run: |
          source .venv/bin/activate
          mypy $(poetry version | cut -d' ' -f1)

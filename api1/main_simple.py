#!/usr/bin/env python3
"""
Simple test version of PropBolt API1 Service
"""

import os
import sys
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Configure CORS
cors_origins = [
    'https://propbolt.com',
    'https://go.propbolt.com', 
    'https://api.propbolt.com',
    'https://data.propbolt.com',
    'https://api1.propbolt.com',
    'http://localhost:3000'
]
CORS(app, origins=cors_origins, supports_credentials=True)

# Root endpoint
@app.route('/', methods=['GET'])
def root():
    """API information"""
    return jsonify({
        'message': 'PropBolt API1 Service - Simple Test Version',
        'version': '1.0.0',
        'status': 'running',
        'timestamp': datetime.utcnow().isoformat()
    })

# Health check endpoint
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0',
        'service': 'api1.propbolt.com'
    })

# Test endpoint
@app.route('/test', methods=['GET', 'POST'])
def test_endpoint():
    """Test endpoint"""
    return jsonify({
        'message': 'Test endpoint working',
        'method': request.method,
        'timestamp': datetime.utcnow().isoformat()
    })

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8080))
    logger.info(f"Starting PropBolt API1 Simple Test Service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=False)

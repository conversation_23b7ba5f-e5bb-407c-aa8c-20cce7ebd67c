#!/usr/bin/env python3
"""
API Key Authentication Middleware
Integrates with existing FastAPI authentication while maintaining backward compatibility
"""

import os
import time
from typing import Optional, Callable, Dict, Any
from fastapi import Request, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response, JSONResponse

from .services import APIKeyService, UsageTrackingService
from .models import APIKey
import logging

logger = logging.getLogger(__name__)


class APIKeyAuthMiddleware(BaseHTTPMiddleware):
    """
    Middleware for API key authentication and usage tracking
    Maintains backward compatibility with existing getenv approach
    """
    
    def __init__(self, app, exclude_paths: Optional[list] = None):
        super().__init__(app)
        self.exclude_paths = exclude_paths or [
            "/docs", "/redoc", "/openapi.json", "/health", "/"
        ]
        # Keep the existing AYO key for backward compatibility
        self.fallback_api_key = os.getenv("API_KEY", "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request through API key authentication"""
        
        # Skip authentication for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        start_time = time.time()
        
        # Extract API key from headers
        api_key = self._extract_api_key(request)
        
        if not api_key:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"error": "API key required", "message": "Please provide a valid API key in the x-api-key header"}
            )
        
        # Validate API key
        api_key_model, is_fallback = await self._validate_api_key(api_key)
        
        if not api_key_model and not is_fallback:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"error": "Invalid API key", "message": "The provided API key is invalid or expired"}
            )
        
        # Check quotas for database-managed keys
        if api_key_model and not is_fallback:
            quota_check = UsageTrackingService.check_quota_exceeded(api_key_model)
            if quota_check["daily_quota_exceeded"]:
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "error": "Quota exceeded",
                        "message": "Daily API quota exceeded",
                        "quota_info": quota_check
                    }
                )
        
        # Add API key info to request state
        request.state.api_key = api_key
        request.state.api_key_model = api_key_model
        request.state.is_fallback_key = is_fallback
        
        # Process the request
        try:
            response = await call_next(request)
            status_code = response.status_code
            error_message = None
        except Exception as e:
            status_code = 500
            error_message = str(e)
            response = JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"error": "Internal server error", "message": "An unexpected error occurred"}
            )
        
        # Log usage for database-managed keys
        if api_key_model and not is_fallback:
            response_time_ms = int((time.time() - start_time) * 1000)
            
            # Extract request/response sizes if available
            request_size = self._get_request_size(request)
            response_size = self._get_response_size(response)
            
            # Log usage asynchronously to avoid blocking
            try:
                await UsageTrackingService.log_api_usage_async(
                    api_key_id=str(api_key_model.id),
                    endpoint=request.url.path,
                    method=request.method,
                    status_code=status_code,
                    ip_address=self._get_client_ip(request),
                    user_agent=request.headers.get("user-agent"),
                    request_size=request_size,
                    response_size=response_size,
                    response_time_ms=response_time_ms,
                    error_message=error_message
                )
            except Exception as e:
                logger.error(f"Failed to log API usage: {e}")
        
        # Add usage info to response headers for database-managed keys
        if api_key_model and not is_fallback:
            quota_info = UsageTrackingService.check_quota_exceeded(api_key_model)
            response.headers["X-RateLimit-Limit"] = str(api_key_model.daily_quota)
            response.headers["X-RateLimit-Remaining"] = str(quota_info["daily_remaining"])
            response.headers["X-RateLimit-Reset"] = str(int(time.time()) + 86400)  # Reset at midnight
        
        return response
    
    def _extract_api_key(self, request: Request) -> Optional[str]:
        """Extract API key from request headers"""
        # Check x-api-key header (primary)
        api_key = request.headers.get("x-api-key")
        if api_key:
            return api_key
        
        # Check Authorization header as fallback
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return auth_header[7:]  # Remove "Bearer " prefix
        
        return None
    
    async def _validate_api_key(self, api_key: str) -> tuple[Optional[APIKey], bool]:
        """
        Validate API key against database and fallback
        Returns (api_key_model, is_fallback)
        """
        # First, check if it's the fallback AYO key
        if api_key == self.fallback_api_key:
            return None, True
        
        # Then check database-managed keys
        try:
            api_key_model = await APIKeyService.validate_api_key_async(api_key)
            if api_key_model:
                return api_key_model, False
        except Exception as e:
            logger.error(f"Database API key validation failed: {e}")
        
        # If database validation fails, check fallback again as safety net
        if api_key == self.fallback_api_key:
            return None, True
        
        return None, False
    
    def _get_client_ip(self, request: Request) -> Optional[str]:
        """Extract client IP address from request"""
        # Check for forwarded headers (common in load balancers)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return None
    
    def _get_request_size(self, request: Request) -> Optional[int]:
        """Get request size in bytes"""
        content_length = request.headers.get("content-length")
        if content_length:
            try:
                return int(content_length)
            except ValueError:
                pass
        return None
    
    def _get_response_size(self, response: Response) -> Optional[int]:
        """Get response size in bytes"""
        if hasattr(response, "headers"):
            content_length = response.headers.get("content-length")
            if content_length:
                try:
                    return int(content_length)
                except ValueError:
                    pass
        return None


class APIKeyDependency:
    """
    FastAPI dependency for API key authentication
    Can be used as an alternative to middleware for specific endpoints
    """
    
    def __init__(self, require_database_key: bool = False):
        self.require_database_key = require_database_key
        self.fallback_api_key = os.getenv("API_KEY", "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914")
    
    async def __call__(self, request: Request) -> Dict[str, Any]:
        """Validate API key and return key information"""
        
        # Extract API key
        api_key = request.headers.get("x-api-key")
        if not api_key:
            auth_header = request.headers.get("authorization")
            if auth_header and auth_header.startswith("Bearer "):
                api_key = auth_header[7:]
        
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API key required"
            )
        
        # Check fallback key first (unless database key is required)
        if not self.require_database_key and api_key == self.fallback_api_key:
            return {
                "api_key": api_key,
                "api_key_model": None,
                "is_fallback": True,
                "quota_info": None
            }
        
        # Validate against database
        api_key_model = await APIKeyService.validate_api_key_async(api_key)
        if not api_key_model:
            if not self.require_database_key and api_key == self.fallback_api_key:
                return {
                    "api_key": api_key,
                    "api_key_model": None,
                    "is_fallback": True,
                    "quota_info": None
                }
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid API key"
            )
        
        # Check quotas
        quota_info = UsageTrackingService.check_quota_exceeded(api_key_model)
        if quota_info["daily_quota_exceeded"]:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Daily API quota exceeded",
                headers={"X-RateLimit-Limit": str(api_key_model.daily_quota)}
            )
        
        return {
            "api_key": api_key,
            "api_key_model": api_key_model,
            "is_fallback": False,
            "quota_info": quota_info
        }


# Convenience instances
api_key_dependency = APIKeyDependency()
database_key_dependency = APIKeyDependency(require_database_key=True)

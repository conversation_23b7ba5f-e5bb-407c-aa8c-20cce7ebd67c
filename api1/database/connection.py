#!/usr/bin/env python3
"""
Database Connection Management for PropBolt API
Handles both sync and async connections to PostgreSQL for cost optimization
"""

import os
import asyncio
from typing import Optional, AsyncGenerator, Generator
from contextlib import contextmanager, asynccontextmanager
from sqlalchemy import create_engine, Engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
import logging

logger = logging.getLogger(__name__)


class DatabaseConfig:
    """Database configuration management"""
    
    def __init__(self):
        # Database connection parameters
        self.db_host = os.getenv("DB_HOST", "*************")
        self.db_port = os.getenv("DB_PORT", "5432")
        self.db_name = os.getenv("DB_NAME", "propbolt")
        self.db_user = os.getenv("DB_USER", "propbolt_user")
        self.db_password = os.getenv("DB_PASSWORD", "PropBolt2024!")
        
        # Connection pool settings
        self.pool_size = int(os.getenv("DB_POOL_SIZE", "5"))
        self.max_overflow = int(os.getenv("DB_MAX_OVERFLOW", "10"))
        self.pool_timeout = int(os.getenv("DB_POOL_TIMEOUT", "30"))
        self.pool_recycle = int(os.getenv("DB_POOL_RECYCLE", "3600"))
        
        # SSL settings for Cloud SQL (prefer allows both encrypted and unencrypted)
        self.ssl_mode = os.getenv("DB_SSL_MODE", "prefer")

        # Cloud SQL connection name for App Engine
        self.cloud_sql_connection = os.getenv("CLOUD_SQL_CONNECTION_NAME")
        
    @property
    def sync_database_url(self) -> str:
        """Get synchronous database URL"""
        # Use Cloud SQL connection if running on App Engine
        if self.cloud_sql_connection and os.getenv("GAE_ENV"):
            return (
                f"postgresql://{self.db_user}:{self.db_password}@"
                f"/{self.db_name}?host=/cloudsql/{self.cloud_sql_connection}"
            )
        else:
            return (
                f"postgresql://{self.db_user}:{self.db_password}@"
                f"{self.db_host}:{self.db_port}/{self.db_name}"
                f"?sslmode={self.ssl_mode}"
            )

    @property
    def async_database_url(self) -> str:
        """Get asynchronous database URL"""
        # Use Cloud SQL connection if running on App Engine
        if self.cloud_sql_connection and os.getenv("GAE_ENV"):
            return (
                f"postgresql+asyncpg://{self.db_user}:{self.db_password}@"
                f"/{self.db_name}?host=/cloudsql/{self.cloud_sql_connection}"
            )
        else:
            # asyncpg uses 'ssl' parameter instead of 'sslmode'
            ssl_param = "require" if self.ssl_mode in ["require", "prefer"] else "disable"
            return (
                f"postgresql+asyncpg://{self.db_user}:{self.db_password}@"
                f"{self.db_host}:{self.db_port}/{self.db_name}"
                f"?ssl={ssl_param}"
            )


class DatabaseManager:
    """Database connection manager with both sync and async support"""
    
    def __init__(self, config: Optional[DatabaseConfig] = None):
        self.config = config or DatabaseConfig()
        self._sync_engine: Optional[Engine] = None
        self._async_engine: Optional[AsyncEngine] = None
        self._sync_session_factory: Optional[sessionmaker] = None
        self._async_session_factory: Optional[async_sessionmaker] = None
        
    def get_sync_engine(self) -> Engine:
        """Get or create synchronous database engine"""
        if self._sync_engine is None:
            self._sync_engine = create_engine(
                self.config.sync_database_url,
                poolclass=QueuePool,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_timeout=self.config.pool_timeout,
                pool_recycle=self.config.pool_recycle,
                echo=os.getenv("DB_ECHO", "false").lower() == "true"
            )
            logger.info("Synchronous database engine created")
        return self._sync_engine
    
    def get_async_engine(self) -> AsyncEngine:
        """Get or create asynchronous database engine"""
        if self._async_engine is None:
            # For async engines, don't specify poolclass - let SQLAlchemy choose the appropriate one
            self._async_engine = create_async_engine(
                self.config.async_database_url,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_timeout=self.config.pool_timeout,
                pool_recycle=self.config.pool_recycle,
                echo=os.getenv("DB_ECHO", "false").lower() == "true"
            )
            logger.info("Asynchronous database engine created")
        return self._async_engine
    
    def get_sync_session_factory(self) -> sessionmaker:
        """Get synchronous session factory"""
        if self._sync_session_factory is None:
            self._sync_session_factory = sessionmaker(
                bind=self.get_sync_engine(),
                autocommit=False,
                autoflush=False
            )
        return self._sync_session_factory
    
    def get_async_session_factory(self) -> async_sessionmaker:
        """Get asynchronous session factory"""
        if self._async_session_factory is None:
            self._async_session_factory = async_sessionmaker(
                bind=self.get_async_engine(),
                autocommit=False,
                autoflush=False
            )
        return self._async_session_factory
    
    @contextmanager
    def get_sync_session(self) -> Generator[Session, None, None]:
        """Context manager for synchronous database sessions"""
        session_factory = self.get_sync_session_factory()
        session = session_factory()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Context manager for asynchronous database sessions"""
        session_factory = self.get_async_session_factory()
        session = session_factory()
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
    
    async def close_async_engine(self):
        """Close asynchronous engine"""
        if self._async_engine:
            await self._async_engine.dispose()
            self._async_engine = None
            logger.info("Asynchronous database engine closed")
    
    def close_sync_engine(self):
        """Close synchronous engine"""
        if self._sync_engine:
            self._sync_engine.dispose()
            self._sync_engine = None
            logger.info("Synchronous database engine closed")
    
    async def test_async_connection(self) -> bool:
        """Test asynchronous database connection"""
        try:
            from sqlalchemy import text
            async with self.get_async_session() as session:
                result = await session.execute(text("SELECT 1"))
                return result.scalar() == 1
        except Exception as e:
            logger.error(f"Async database connection test failed: {e}")
            return False
    
    def test_sync_connection(self) -> bool:
        """Test synchronous database connection"""
        try:
            from sqlalchemy import text
            with self.get_sync_session() as session:
                result = session.execute(text("SELECT 1"))
                return result.scalar() == 1
        except Exception as e:
            logger.error(f"Sync database connection test failed: {e}")
            return False


# Global database manager instance
db_manager = DatabaseManager()


# Convenience functions for getting database sessions
def get_sync_db_session() -> Generator[Session, None, None]:
    """Get synchronous database session"""
    return db_manager.get_sync_session()


async def get_async_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Get asynchronous database session"""
    async with db_manager.get_async_session() as session:
        yield session


# Health check functions
async def check_database_health() -> dict:
    """Check database health for both sync and async connections"""
    sync_healthy = db_manager.test_sync_connection()
    async_healthy = await db_manager.test_async_connection()
    
    return {
        "database": {
            "sync_connection": "healthy" if sync_healthy else "unhealthy",
            "async_connection": "healthy" if async_healthy else "unhealthy",
            "overall": "healthy" if (sync_healthy and async_healthy) else "unhealthy"
        }
    }


# Startup and shutdown handlers for FastAPI
async def startup_database():
    """Initialize database connections on startup"""
    logger.info("Initializing database connections...")
    
    # Test connections
    sync_ok = db_manager.test_sync_connection()
    async_ok = await db_manager.test_async_connection()
    
    if not sync_ok:
        logger.error("Failed to establish synchronous database connection")
        raise Exception("Synchronous database connection failed")
    
    if not async_ok:
        logger.error("Failed to establish asynchronous database connection")
        raise Exception("Asynchronous database connection failed")
    
    logger.info("Database connections established successfully")


async def shutdown_database():
    """Clean up database connections on shutdown"""
    logger.info("Closing database connections...")
    await db_manager.close_async_engine()
    db_manager.close_sync_engine()
    logger.info("Database connections closed")

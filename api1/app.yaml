runtime: python39
service: api1

# Automatic scaling configuration
automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.6

# Environment variables
env_variables:
  # Server Configuration
  PORT: 8080
  FLASK_ENV: production
  DEBUG_MODE: false
  
  # Database Configuration (Google Cloud PostgreSQL)
  DB_HOST: *************
  DB_PORT: 5432
  DB_NAME: propbolt
  DB_USER: propbolt_user
  DB_PASSWORD: PropBolt2024!
  DB_SSL_MODE: require
  DATABASE_URL: ***********************************************************/propbolt?sslmode=require
  
  # External API Configuration
  API_KEY: AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914
  REAL_ESTATE_API_KEY: AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914
  REAL_ESTATE_API_URL: https://api.realestateapi.com
  
  # Google Cloud Configuration
  GCP_PROJECT_ID: gold-braid-458901-v2
  GCP_PROJECT_NUMBER: ************
  GCP_REGION: us-central1
  GOOGLE_APPLICATION_CREDENTIALS: propbolt-service-account-key.json
  GOOGLE_MAPS_API_KEY: AIzaSyBbnrFdaQisjmNJzviY9s9GrgxQvAdM6k4
  MAPTILER_API_KEY: wBfumy70aE1pm6PGKkiU
  
  # CORS Configuration
  CORS_ALLOWED_ORIGINS: https://propbolt.com,https://go.propbolt.com,https://api.propbolt.com,https://data.propbolt.com,https://api1.propbolt.com,http://localhost:3000
  CORS_ALLOW_CREDENTIALS: true
  
  # Feature Flags
  FEATURE_DATA_ANALYTICS: true
  FEATURE_BULK_PROCESSING: true
  FEATURE_EXPORT: true
  
  # Logging Configuration
  LOG_LEVEL: info

# Request handlers
handlers:
- url: /.*
  script: auto

# Health check configuration
readiness_check:
  path: "/health"
  check_interval_sec: 5
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2
  app_start_timeout_sec: 300

liveness_check:
  path: "/health"
  check_interval_sec: 30
  timeout_sec: 4
  failure_threshold: 4
  success_threshold: 2

# Resource limits
resources:
  cpu: 1
  memory_gb: 0.5
  disk_size_gb: 10

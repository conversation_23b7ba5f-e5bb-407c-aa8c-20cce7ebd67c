# PropBolt API1 Service Dependencies
# Flask-based API with Google Cloud Endpoints integration

# Web Framework
Flask==2.3.3
Werkzeug==2.3.7

# CORS Support
Flask-CORS==4.0.0

# Database
psycopg2-binary==2.9.7
SQLAlchemy==2.0.23
asyncpg==0.29.0

# HTTP Requests
requests==2.31.0
httpx>=0.26.0,<1

# Environment Variables
python-dotenv==1.0.0

# JSON Processing
ujson==5.8.0

# Date/Time Utilities
python-dateutil==2.8.2

# Original Real Estate API SDK Dependencies
pydantic>=2.5.0,<3.0.0
typing_extensions>=4.0.0
jsonpointer>=3.0.0

# Google Cloud
google-cloud-core==2.3.3
google-cloud-storage==2.10.0
google-cloud-endpoints==4.9.0

# Security
cryptography==41.0.4

# Performance
gunicorn==21.2.0

# API Documentation
flask-swagger-ui==4.11.1

# Async support
greenlet>=2.0.0

# Additional utilities for PropBolt integration
alembic>=1.13.0

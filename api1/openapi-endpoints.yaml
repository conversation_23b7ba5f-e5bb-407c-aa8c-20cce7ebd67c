swagger: '2.0'
info:
  title: PropBolt API1 Service
  description: Real Estate Data API Service for api1.propbolt.com using Google Cloud Endpoints
  version: 1.0.0
  contact:
    name: <PERSON>pBolt Support
    email: <EMAIL>
    url: https://propbolt.com

host: api1.propbolt.com
schemes:
  - https

produces:
  - application/json

securityDefinitions:
  api_key:
    type: apiKey
    name: x-api-key
    in: header
    description: API key for authentication

security:
  - api_key: []

paths:
  /:
    get:
      summary: API Information
      description: Get API information and available endpoints
      operationId: root
      responses:
        200:
          description: API information
          schema:
            type: object
            properties:
              message:
                type: string
              version:
                type: string
              endpoints:
                type: object

  /health:
    get:
      summary: Health Check
      description: Health check endpoint for monitoring
      operationId: health_check
      security: []
      responses:
        200:
          description: Service health status
          schema:
            type: object
            properties:
              status:
                type: string
              timestamp:
                type: string
              version:
                type: string

  /v2/PropertySearch:
    post:
      summary: Property Search API
      description: Searchable API for list building, search counts, and advanced filtering on properties
      operationId: property_search
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              address:
                type: string
                description: Property address
              city:
                type: string
                description: City name
              state:
                type: string
                description: State abbreviation
              zip:
                type: string
                description: ZIP code
              beds:
                type: object
                description: Bedroom filter
              baths:
                type: object
                description: Bathroom filter
              value:
                type: object
                description: Property value filter
              size:
                type: integer
                description: Number of results to return
                default: 50
      responses:
        200:
          description: Property search results
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
              timestamp:
                type: string
        401:
          description: Unauthorized - Invalid API key
        429:
          description: Rate limit exceeded

  /v2/PropertyDetail:
    post:
      summary: Property Detail API
      description: Get comprehensive property information including ownership, sales history, and valuations
      operationId: property_detail
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              address:
                type: string
                description: Property address
              id:
                type: string
                description: Property ID
              apn:
                type: string
                description: Assessor Parcel Number
              city:
                type: string
                description: City name
              state:
                type: string
                description: State abbreviation
              zip:
                type: string
                description: ZIP code
              exact_match:
                type: boolean
                description: Require exact address match
                default: false
              comps:
                type: boolean
                description: Include comparable properties
                default: false
      responses:
        200:
          description: Property details
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
              timestamp:
                type: string
        401:
          description: Unauthorized - Invalid API key
        429:
          description: Rate limit exceeded

  /v2/PropertyDetailBulk:
    post:
      summary: Property Detail Bulk API
      description: Retrieve up to 1000 properties at once
      operationId: property_detail_bulk
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              ids:
                type: array
                items:
                  type: string
                description: Array of property IDs
      responses:
        200:
          description: Bulk property details
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
              timestamp:
                type: string
        401:
          description: Unauthorized - Invalid API key
        429:
          description: Rate limit exceeded

  /v1/PropertyParcel:
    post:
      summary: Property Boundary API
      description: Returns parcel boundaries in GEOJSON format
      operationId: property_parcel
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              address:
                type: string
                description: Property address
              id:
                type: string
                description: Property ID
              apn:
                type: string
                description: Assessor Parcel Number
      responses:
        200:
          description: Property parcel boundaries
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
              timestamp:
                type: string
        401:
          description: Unauthorized - Invalid API key
        429:
          description: Rate limit exceeded

  /v2/PropertyComps:
    post:
      summary: Property Comparables API v2
      description: Generate property comparables for valuation
      operationId: property_comps_v2
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              address:
                type: string
                description: Property address
              id:
                type: string
                description: Property ID
      responses:
        200:
          description: Property comparables
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
              timestamp:
                type: string
        401:
          description: Unauthorized - Invalid API key
        429:
          description: Rate limit exceeded

  /v3/PropertyComps:
    post:
      summary: Property Comparables API v3
      description: Advanced comparables with customizable parameters
      operationId: property_comps_v3
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              address:
                type: string
                description: Property address
              id:
                type: string
                description: Property ID
              max_radius_miles:
                type: number
                description: Maximum search radius in miles
                default: 1.0
              max_days_back:
                type: integer
                description: Maximum days back for sales data
                default: 180
              max_results:
                type: integer
                description: Maximum number of comparables
                default: 10
      responses:
        200:
          description: Advanced property comparables
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
              timestamp:
                type: string
        401:
          description: Unauthorized - Invalid API key
        429:
          description: Rate limit exceeded

  /v2/AutoComplete:
    post:
      summary: AutoComplete API
      description: Property search based on incomplete address parts
      operationId: autocomplete
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              search:
                type: string
                description: Partial address to search
              latitude:
                type: number
                description: Latitude for location-based search
              longitude:
                type: number
                description: Longitude for location-based search
      responses:
        200:
          description: Address autocomplete suggestions
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
              timestamp:
                type: string
        401:
          description: Unauthorized - Invalid API key
        429:
          description: Rate limit exceeded

  /v2/AddressVerification:
    post:
      summary: Address Verification API
      description: Verify 1-100 addresses for accuracy
      operationId: address_verification
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              addresses:
                type: array
                items:
                  type: object
                description: Array of addresses to verify
              strict:
                type: boolean
                description: Use strict verification mode
                default: false
      responses:
        200:
          description: Address verification results
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
              timestamp:
                type: string
        401:
          description: Unauthorized - Invalid API key
        429:
          description: Rate limit exceeded

  /v2/PropGPT:
    post:
      summary: PropGPT API
      description: Natural language property search using AI
      operationId: propgpt
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              query:
                type: string
                description: Natural language query
              size:
                type: integer
                description: Number of results
                default: 50
              model:
                type: string
                description: AI model to use
                default: "gpt-4o"
      responses:
        200:
          description: AI-powered property search results
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
              timestamp:
                type: string
        401:
          description: Unauthorized - Invalid API key
        429:
          description: Rate limit exceeded

  /v2/CSVBuilder:
    post:
      summary: CSV Generator API
      description: Generate CSV exports of property data
      operationId: csv_builder
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              file_name:
                type: string
                description: Name for the CSV file
              map:
                type: array
                items:
                  type: string
                description: Fields to include in CSV
      responses:
        200:
          description: CSV generation result
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
              timestamp:
                type: string
        401:
          description: Unauthorized - Invalid API key
        429:
          description: Rate limit exceeded

  /v2/PropertyAvm:
    post:
      summary: Lender Grade AVM API
      description: Get precise property valuations
      operationId: property_avm
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              address:
                type: string
                description: Property address
              id:
                type: string
                description: Property ID
              strict:
                type: boolean
                description: Use strict valuation mode
                default: false
      responses:
        200:
          description: Property valuation
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
              timestamp:
                type: string
        401:
          description: Unauthorized - Invalid API key
        429:
          description: Rate limit exceeded

  /v2/Reports/PropertyLiens:
    post:
      summary: Involuntary Liens API
      description: Get property lien information
      operationId: property_liens
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              address:
                type: string
                description: Property address
              id:
                type: string
                description: Property ID
              zip:
                type: string
                description: ZIP code
              apn:
                type: string
                description: Assessor Parcel Number
      responses:
        200:
          description: Property lien information
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
              timestamp:
                type: string
        401:
          description: Unauthorized - Invalid API key
        429:
          description: Rate limit exceeded

  /v2/PropertyMapping:
    post:
      summary: Mapping (Pins) API
      description: Create map pins for PropTech applications
      operationId: property_mapping
      consumes:
        - application/json
      parameters:
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              address:
                type: string
                description: Property address
              city:
                type: string
                description: City name
              state:
                type: string
                description: State abbreviation
              zip:
                type: string
                description: ZIP code
              size:
                type: integer
                description: Number of pins to return
                default: 50
      responses:
        200:
          description: Property mapping pins
          schema:
            type: object
            properties:
              success:
                type: boolean
              data:
                type: object
              timestamp:
                type: string
        401:
          description: Unauthorized - Invalid API key
        429:
          description: Rate limit exceeded

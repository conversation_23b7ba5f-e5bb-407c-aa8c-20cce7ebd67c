#!/bin/bash

# =============================================================================
# PropBolt API1 Service Deployment Script
# Deploys api1.propbolt.com using Google Cloud Endpoints and App Engine
# =============================================================================

set -e  # Exit on any error

echo "🚀 PropBolt API1 Service Deployment"
echo "===================================="

# Configuration
PROJECT_ID="gold-braid-458901-v2"
SERVICE_NAME="api1"
REGION="us-central1"
DOMAIN="api1.propbolt.com"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if gcloud is installed and authenticated
check_gcloud() {
    log_info "Checking gcloud configuration..."
    
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "Not authenticated with gcloud. Please run 'gcloud auth login'"
        exit 1
    fi
    
    # Set project
    gcloud config set project $PROJECT_ID
    log_success "gcloud configured for project: $PROJECT_ID"
}

# App Engine Standard deployment (no Endpoints needed)
deploy_endpoints() {
    log_info "Using App Engine Standard with built-in load balancing and auto-scaling"
    log_info "API documentation will be available at https://$DOMAIN/docs"
    log_success "Ready for App Engine deployment"
}

# Deploy App Engine service
deploy_app_engine() {
    log_info "Deploying App Engine service..."
    
    # Deploy the service
    gcloud app deploy app.yaml --quiet --promote
    
    if [ $? -eq 0 ]; then
        log_success "App Engine service deployed successfully"
    else
        log_error "Failed to deploy App Engine service"
        exit 1
    fi
}

# Configure custom domain mapping
configure_domain() {
    log_info "Configuring custom domain mapping..."
    
    # Check if domain mapping already exists
    if gcloud app domain-mappings list --filter="id:$DOMAIN" --format="value(id)" | grep -q $DOMAIN; then
        log_warning "Domain mapping for $DOMAIN already exists"
    else
        # Create domain mapping
        gcloud app domain-mappings create $DOMAIN
        
        if [ $? -eq 0 ]; then
            log_success "Domain mapping created for $DOMAIN"
            log_info "Please configure DNS records as shown above"
        else
            log_error "Failed to create domain mapping"
            exit 1
        fi
    fi
}

# Test the deployed service
test_service() {
    log_info "Testing deployed service..."
    
    # Test health endpoint
    HEALTH_URL="https://$DOMAIN/health"
    log_info "Testing health endpoint: $HEALTH_URL"
    
    if curl -s -f "$HEALTH_URL" > /dev/null; then
        log_success "Health check passed"
    else
        log_warning "Health check failed - service may still be starting up"
    fi
    
    # Test root endpoint
    ROOT_URL="https://$DOMAIN/"
    log_info "Testing root endpoint: $ROOT_URL"
    
    if curl -s -f "$ROOT_URL" > /dev/null; then
        log_success "Root endpoint accessible"
    else
        log_warning "Root endpoint test failed"
    fi
}

# Main deployment process
main() {
    log_info "Starting deployment process..."
    
    # Change to api1 directory
    cd "$(dirname "$0")"
    
    # Check prerequisites
    check_gcloud
    
    # Deploy components
    deploy_endpoints
    deploy_app_engine
    configure_domain
    
    # Test deployment
    test_service
    
    log_success "Deployment completed successfully!"
    echo ""
    echo "🌐 Service URLs:"
    echo "   • API1 Service: https://$DOMAIN"
    echo "   • Health Check: https://$DOMAIN/health"
    echo "   • API Documentation: https://$DOMAIN/docs"
    echo ""
    echo "📊 Monitoring:"
    echo "   • App Engine: https://console.cloud.google.com/appengine/services?project=$PROJECT_ID"
    echo "   • Endpoints: https://console.cloud.google.com/endpoints/api/$DOMAIN/overview?project=$PROJECT_ID"
    echo "   • Logs: https://console.cloud.google.com/logs/query?project=$PROJECT_ID"
    echo ""
    log_info "Deployment complete! 🎉"
}

# Run main function
main "$@"

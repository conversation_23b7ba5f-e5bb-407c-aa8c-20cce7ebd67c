#!/usr/bin/env python3
"""
PropBolt Real Estate API - Original SDK Implementation
Enterprise-grade real estate data API for data.propbolt.com with API key management
"""

import os
import logging
from flask import Flask, jsonify, request
from flask_cors import CORS
from datetime import datetime, timezone

# Import the original Real Estate API SDK
from real_estate_api_py import RealEstateApiPy
from real_estate_api_py.environment import RealEstateApiPyEnvironment

# Import API key management components
try:
    from .api_key_manager import APIKeyManager
    from .middleware import APIKeyMiddleware, require_api_key, get_current_api_key_info
    from .admin_routes import admin_bp
except ImportError:
    # Fallback for direct execution
    from api_key_manager import APIKeyManager
    from middleware import APIKeyMiddleware, require_api_key, get_current_api_key_info
    from admin_routes import admin_bp

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO').upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Configure CORS
cors_origins = os.getenv('CORS_ALLOWED_ORIGINS', 'http://localhost:3000').split(',')
CORS(app, origins=cors_origins, supports_credentials=True)

# Initialize API key management
api_key_manager = APIKeyManager()
api_key_middleware = APIKeyMiddleware(app)

# Register admin blueprint
app.register_blueprint(admin_bp)

# External API configuration
REAL_ESTATE_API_KEY = os.getenv('REAL_ESTATE_API_KEY')
REAL_ESTATE_API_URL = os.getenv('REAL_ESTATE_API_URL')

def validate_api_key_direct(request):
    """Direct API key validation without middleware"""
    # Extract API key from request
    api_key = None

    # Check Authorization header (Bearer token)
    auth_header = request.headers.get('Authorization', '')
    if auth_header.startswith('Bearer '):
        api_key = auth_header[7:]  # Remove 'Bearer ' prefix

    # Check X-API-Key header
    if not api_key:
        api_key = request.headers.get('X-API-Key')

    # Check query parameter
    if not api_key:
        api_key = request.args.get('api_key')

    if not api_key:
        return None, {"error": "API key required", "message": "Please provide a valid API key"}, 401

    # Validate API key using our manager
    try:
        key_info = api_key_manager.validate_api_key(api_key)
        if not key_info:
            return None, {"error": "Invalid API key", "message": "The provided API key is invalid"}, 401

        # Check rate limits
        allowed, remaining = api_key_manager.check_rate_limit(
            key_info['id'], key_info['requests_per_minute']
        )

        if not allowed:
            return None, {
                "error": "Rate limit exceeded",
                "message": f"Rate limit of {key_info['requests_per_minute']} requests per minute exceeded"
            }, 429

        return key_info, None, None

    except Exception as e:
        logger.error(f"API key validation error: {e}")
        return None, {"error": "Validation failed", "message": "Unable to validate API key"}, 500

def proxy_to_real_estate_api(endpoint_path, user_data=None):
    """Helper function to proxy requests to the real estate API using admin key"""
    try:
        headers = {
            'x-api-key': REAL_ESTATE_API_KEY,
            'Content-Type': 'application/json'
        }

        # Use the correct external API URL - RealEstateAPI.com
        api_url = 'https://api.realestateapi.com'

        logger.info(f"Proxying request to {api_url}{endpoint_path} with admin key")

        # Make request to the actual real estate API
        response = requests.post(f"{api_url}{endpoint_path}",
                               headers=headers, json=user_data, timeout=30)

        logger.info(f"Real estate API response: {response.status_code}")

        # Return the actual response from the real estate API
        try:
            return response.json(), response.status_code
        except ValueError:
            # If response is not JSON, return as text
            return {"data": response.text}, response.status_code

    except requests.exceptions.RequestException as e:
        logger.error(f"Real estate API request failed for {endpoint_path}: {e}")
        return {
            "error": "Service temporarily unavailable",
            "message": f"Unable to process {endpoint_path} request",
            "details": str(e)
        }, 503
    except Exception as e:
        logger.error(f"Proxy error for {endpoint_path}: {e}")
        return {
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "details": str(e)
        }, 500

@app.route('/', methods=['GET'])
def root():
    """Root endpoint with API information"""
    return jsonify({
        "message": "PropBolt Real Estate API",
        "version": "1.0.0",
        "description": "Complete 13 Endpoint Implementation",
        "endpoints": {
            "health": "GET /health",
            "property_search": "POST /v2/PropertySearch",
            "property_details": "POST /v2/PropertyDetail",
            "property_detail_bulk": "POST /v2/PropertyDetailBulk",
            "property_parcel": "POST /v1/PropertyParcel",
            "property_comps_v2": "POST /v2/PropertyComps",
            "property_comps_v3": "POST /v3/PropertyComps",
            "autocomplete": "POST /v2/AutoComplete",
            "address_verification": "POST /v2/AddressVerification",
            "propgpt": "POST /v2/PropGPT",
            "csv_builder": "POST /v2/CSVBuilder",
            "property_avm": "POST /v2/PropertyAvm",
            "property_liens": "POST /v2/Reports/PropertyLiens",
            "property_mapping": "POST /v2/PropertyMapping"
        },
        "documentation": "/docs",
        "timestamp": datetime.utcnow().isoformat()
    })

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for Google Cloud"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "api_key_configured": bool(REAL_ESTATE_API_KEY),
        "sdk_clients_available": True,
        "mode": "production"
    })

@app.route('/debug', methods=['GET'])
def debug_info():
    """Debug endpoint to check configuration"""
    return jsonify({
        "admin_api_key_configured": bool(REAL_ESTATE_API_KEY),
        "admin_api_key_prefix": REAL_ESTATE_API_KEY[:20] + "..." if REAL_ESTATE_API_KEY else None,
        "api_url_configured": bool(REAL_ESTATE_API_URL),
        "api_url": REAL_ESTATE_API_URL,
        "middleware_initialized": hasattr(app, 'before_request_funcs'),
        "db_host": os.getenv('DB_HOST'),
        "db_name": os.getenv('DB_NAME'),
        "timestamp": datetime.utcnow().isoformat()
    })

@app.route('/docs', methods=['GET'])
def docs():
    """API documentation endpoint"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>PropBolt API - Swagger UI</title>
        <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui.css" />
    </head>
    <body>
        <div id="swagger-ui"></div>
        <script src="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui-bundle.js"></script>
        <script>
            SwaggerUIBundle({
                url: '/openapi.json',
                dom_id: '#swagger-ui',
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIBundle.presets.standalone
                ]
            });
        </script>
    </body>
    </html>
    """

# Real Estate API Endpoints (matching the deployed service) - All require API keys
@app.route('/v2/PropertySearch', methods=['POST'])
def property_search():
    """Property search endpoint - Proxy to real estate API"""
    # Validate API key directly
    key_info, error_response, status_code = validate_api_key_direct(request)
    if error_response:
        return jsonify(error_response), status_code

    # Log usage
    api_key_manager.log_api_usage(key_info['id'], '/v2/PropertySearch', 'POST')

    # Proxy to real estate API
    user_data = request.get_json()
    result, status_code = proxy_to_real_estate_api('/v2/PropertySearch', user_data)
    return jsonify(result), status_code

@app.route('/v2/PropertyDetail', methods=['POST'])
def property_detail():
    """Property detail endpoint - Proxy to real estate API"""
    # Validate API key directly
    key_info, error_response, status_code = validate_api_key_direct(request)
    if error_response:
        return jsonify(error_response), status_code

    # Log usage
    api_key_manager.log_api_usage(key_info['id'], '/v2/PropertyDetail', 'POST')

    # Proxy to real estate API
    user_data = request.get_json()
    result, status_code = proxy_to_real_estate_api('/v2/PropertyDetail', user_data)
    return jsonify(result), status_code

@app.route('/v2/PropertyDetailBulk', methods=['POST'])
def property_detail_bulk():
    """Bulk property detail endpoint - Proxy to real estate API"""
    # Validate API key directly
    key_info, error_response, status_code = validate_api_key_direct(request)
    if error_response:
        return jsonify(error_response), status_code

    # Log usage
    api_key_manager.log_api_usage(key_info['id'], '/v2/PropertyDetailBulk', 'POST')

    # Proxy to real estate API
    user_data = request.get_json()
    result, status_code = proxy_to_real_estate_api('/v2/PropertyDetailBulk', user_data)
    return jsonify(result), status_code

@app.route('/v1/PropertyParcel', methods=['POST'])
def property_parcel():
    """Property parcel endpoint - Proxy to real estate API"""
    # Validate API key directly
    key_info, error_response, status_code = validate_api_key_direct(request)
    if error_response:
        return jsonify(error_response), status_code

    # Log usage
    api_key_manager.log_api_usage(key_info['id'], '/v1/PropertyParcel', 'POST')

    # Proxy to real estate API
    user_data = request.get_json()
    result, status_code = proxy_to_real_estate_api('/v1/PropertyParcel', user_data)
    return jsonify(result), status_code

@app.route('/v2/PropertyComps', methods=['POST'])
@require_api_key
def property_comps_v2():
    """Property comparables v2 endpoint - Proxy to real estate API"""
    user_data = request.get_json()
    result, status_code = proxy_to_real_estate_api('/v2/PropertyComps', user_data)
    return jsonify(result), status_code

@app.route('/v3/PropertyComps', methods=['POST'])
@require_api_key
def property_comps_v3():
    """Property comparables v3 endpoint - Proxy to real estate API"""
    user_data = request.get_json()
    result, status_code = proxy_to_real_estate_api('/v3/PropertyComps', user_data)
    return jsonify(result), status_code

@app.route('/v2/AutoComplete', methods=['POST'])
@require_api_key
def autocomplete():
    """Address autocomplete endpoint - Proxy to real estate API"""
    user_data = request.get_json()
    result, status_code = proxy_to_real_estate_api('/v2/AutoComplete', user_data)
    return jsonify(result), status_code

@app.route('/v2/AddressVerification', methods=['POST'])
@require_api_key
def address_verification():
    """Address verification endpoint - Proxy to real estate API"""
    user_data = request.get_json()
    result, status_code = proxy_to_real_estate_api('/v2/AddressVerification', user_data)
    return jsonify(result), status_code

@app.route('/v2/PropGPT', methods=['POST'])
@require_api_key
def propgpt():
    """PropGPT AI endpoint - Proxy to real estate API"""
    user_data = request.get_json()
    result, status_code = proxy_to_real_estate_api('/v2/PropGPT', user_data)
    return jsonify(result), status_code

@app.route('/v2/CSVBuilder', methods=['POST'])
@require_api_key
def csv_builder():
    """CSV builder endpoint - Proxy to real estate API"""
    user_data = request.get_json()
    result, status_code = proxy_to_real_estate_api('/v2/CSVBuilder', user_data)
    return jsonify(result), status_code

@app.route('/v2/PropertyAvm', methods=['POST'])
@require_api_key
def property_avm():
    """Property AVM endpoint - Proxy to real estate API"""
    user_data = request.get_json()
    result, status_code = proxy_to_real_estate_api('/v2/PropertyAvm', user_data)
    return jsonify(result), status_code

@app.route('/v2/Reports/PropertyLiens', methods=['POST'])
@require_api_key
def property_liens():
    """Property liens report endpoint - Proxy to real estate API"""
    user_data = request.get_json()
    result, status_code = proxy_to_real_estate_api('/v2/Reports/PropertyLiens', user_data)
    return jsonify(result), status_code

@app.route('/v2/PropertyMapping', methods=['POST'])
@require_api_key
def property_mapping():
    """Property mapping endpoint - Proxy to real estate API"""
    user_data = request.get_json()
    result, status_code = proxy_to_real_estate_api('/v2/PropertyMapping', user_data)
    return jsonify(result), status_code

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8081))
    debug = os.getenv('DEBUG_MODE', 'false').lower() == 'true'

    logger.info(f"Starting PropBolt Real Estate API on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)

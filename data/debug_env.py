#!/usr/bin/env python3
"""
Debug script to check environment variables and API key configuration
"""

import os
import sys

def check_environment():
    """Check all environment variables"""
    print("🔍 Environment Variables Check")
    print("=" * 50)
    
    # Check critical environment variables
    env_vars = [
        'REAL_ESTATE_API_KEY',
        'REAL_ESTATE_API_URL',
        'DB_HOST',
        'DB_NAME',
        'DB_USER',
        'DB_PASSWORD'
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            if 'PASSWORD' in var or 'KEY' in var:
                masked_value = value[:8] + '*' * (len(value) - 8) if len(value) > 8 else '*' * len(value)
                print(f"✅ {var}: {masked_value}")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: NOT SET")
    
    print("\n🔍 Testing API Key Configuration")
    print("=" * 50)
    
    api_key = os.getenv('REAL_ESTATE_API_KEY')
    if api_key:
        print(f"✅ Admin API Key found: {api_key[:20]}...")
        print(f"✅ Key length: {len(api_key)}")
        print(f"✅ Key starts with expected prefix: {api_key.startswith('AYOKASYSTEMS')}")
    else:
        print("❌ Admin API Key not found!")
    
    api_url = os.getenv('REAL_ESTATE_API_URL')
    if api_url:
        print(f"✅ API URL: {api_url}")
    else:
        print("❌ API URL not found!")

if __name__ == '__main__':
    check_environment()

#!/usr/bin/env python3
"""
PropBolt API Key Management - Admin Routes
Admin endpoints for managing API keys, usage analytics, and quotas
"""

import logging
from flask import Blueprint, request, jsonify
from datetime import datetime
try:
    from .api_key_manager import APIKeyManager
    from .middleware import require_admin, get_current_api_key_info
except ImportError:
    from api_key_manager import APIKeyManager
    from middleware import require_admin, get_current_api_key_info

logger = logging.getLogger(__name__)

# Create admin blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')
api_key_manager = APIKeyManager()

@admin_bp.route('/keys/generate', methods=['POST'])
@require_admin
def generate_api_key():
    """Generate a new API key"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['user_id', 'name']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'error': 'Missing required field',
                    'message': f'Field "{field}" is required'
                }), 400
        
        user_id = data['user_id']
        name = data['name']
        tier = data.get('tier', 'basic')
        key_type = data.get('key_type', 'live')
        expires_days = data.get('expires_days')
        
        # Validate tier
        valid_tiers = ['basic', 'premium', 'enterprise']
        if tier not in valid_tiers:
            return jsonify({
                'error': 'Invalid tier',
                'message': f'Tier must be one of: {", ".join(valid_tiers)}'
            }), 400
        
        # Validate key type
        if key_type not in ['live', 'test']:
            return jsonify({
                'error': 'Invalid key type',
                'message': 'Key type must be "live" or "test"'
            }), 400
        
        # Generate API key
        key_info = api_key_manager.create_api_key(
            user_id=user_id,
            name=name,
            tier=tier,
            key_type=key_type,
            expires_days=expires_days
        )
        
        return jsonify({
            'success': True,
            'message': 'API key generated successfully',
            'data': key_info
        }), 201
        
    except ValueError as e:
        return jsonify({
            'error': 'Validation error',
            'message': str(e)
        }), 400
    except Exception as e:
        logger.error(f"Failed to generate API key: {e}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'Failed to generate API key'
        }), 500

@admin_bp.route('/keys', methods=['GET'])
@require_admin
def list_api_keys():
    """List all API keys or keys for a specific user"""
    try:
        user_id = request.args.get('user_id')
        
        keys = api_key_manager.get_api_keys(user_id=user_id)
        
        # Remove sensitive information
        safe_keys = []
        for key in keys:
            safe_key = {
                'id': key['id'],
                'key_prefix': key['key_prefix'],
                'user_id': key['user_id'],
                'user_email': key.get('user_email'),
                'user_name': key.get('user_name'),
                'name': key['name'],
                'tier': key['tier'],
                'is_active': key['is_active'],
                'created_at': key['created_at'],
                'expires_at': key['expires_at'],
                'revoked_at': key['revoked_at'],
                'last_used_at': key['last_used_at'],
                'requests_per_minute': key['requests_per_minute'],
                'requests_per_day': key['requests_per_day'],
                'daily_requests': key.get('daily_requests', 0),
                'last_usage_date': key.get('last_usage_date')
            }
            safe_keys.append(safe_key)
        
        return jsonify({
            'success': True,
            'data': safe_keys,
            'count': len(safe_keys)
        })
        
    except Exception as e:
        logger.error(f"Failed to list API keys: {e}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'Failed to retrieve API keys'
        }), 500

@admin_bp.route('/keys/<key_id>', methods=['DELETE'])
@require_admin
def revoke_api_key(key_id):
    """Revoke an API key"""
    try:
        success = api_key_manager.revoke_api_key(key_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'API key revoked successfully'
            })
        else:
            return jsonify({
                'error': 'API key not found',
                'message': 'The specified API key was not found'
            }), 404
            
    except Exception as e:
        logger.error(f"Failed to revoke API key: {e}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'Failed to revoke API key'
        }), 500

@admin_bp.route('/keys/<key_id>', methods=['PUT'])
@require_admin
def update_api_key(key_id):
    """Update API key settings (quotas, expiration, etc.)"""
    try:
        data = request.get_json()
        
        # TODO: Implement API key update functionality
        # This would involve updating quotas, expiration dates, allowed endpoints, etc.
        
        return jsonify({
            'error': 'Not implemented',
            'message': 'API key update functionality is not yet implemented'
        }), 501
        
    except Exception as e:
        logger.error(f"Failed to update API key: {e}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'Failed to update API key'
        }), 500

@admin_bp.route('/analytics', methods=['GET'])
@require_admin
def get_analytics():
    """Get overall API usage analytics"""
    try:
        # TODO: Implement comprehensive analytics
        # This would include overall usage statistics, top endpoints, error rates, etc.
        
        return jsonify({
            'error': 'Not implemented',
            'message': 'Analytics endpoint is not yet implemented'
        }), 501
        
    except Exception as e:
        logger.error(f"Failed to get analytics: {e}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'Failed to retrieve analytics'
        }), 500

@admin_bp.route('/usage/<key_id>', methods=['GET'])
@require_admin
def get_key_usage(key_id):
    """Get usage analytics for a specific API key"""
    try:
        days = request.args.get('days', 30, type=int)
        
        if days < 1 or days > 365:
            return jsonify({
                'error': 'Invalid days parameter',
                'message': 'Days must be between 1 and 365'
            }), 400
        
        analytics = api_key_manager.get_usage_analytics(key_id, days)
        
        if not analytics:
            return jsonify({
                'error': 'API key not found',
                'message': 'The specified API key was not found'
            }), 404
        
        return jsonify({
            'success': True,
            'data': analytics
        })
        
    except Exception as e:
        logger.error(f"Failed to get key usage: {e}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'Failed to retrieve usage analytics'
        }), 500

@admin_bp.route('/tiers', methods=['GET'])
@require_admin
def get_tiers():
    """Get available API key tiers and their configurations"""
    try:
        tiers = api_key_manager.tier_configs
        
        return jsonify({
            'success': True,
            'data': tiers
        })
        
    except Exception as e:
        logger.error(f"Failed to get tiers: {e}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'Failed to retrieve tier information'
        }), 500

@admin_bp.route('/stats', methods=['GET'])
@require_admin
def get_stats():
    """Get overall API statistics"""
    try:
        # TODO: Implement comprehensive statistics
        # This would include total keys, active keys, total requests, etc.
        
        return jsonify({
            'success': True,
            'data': {
                'message': 'Statistics endpoint is not yet fully implemented',
                'placeholder_stats': {
                    'total_keys': 0,
                    'active_keys': 0,
                    'total_requests_today': 0,
                    'total_requests_this_month': 0
                }
            }
        })
        
    except Exception as e:
        logger.error(f"Failed to get stats: {e}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'Failed to retrieve statistics'
        }), 500

# Error handlers for the admin blueprint
@admin_bp.errorhandler(400)
def bad_request(error):
    return jsonify({
        'error': 'Bad request',
        'message': 'The request was invalid'
    }), 400

@admin_bp.errorhandler(401)
def unauthorized(error):
    return jsonify({
        'error': 'Unauthorized',
        'message': 'Admin access required'
    }), 401

@admin_bp.errorhandler(403)
def forbidden(error):
    return jsonify({
        'error': 'Forbidden',
        'message': 'Access denied'
    }), 403

@admin_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'error': 'Not found',
        'message': 'The requested resource was not found'
    }), 404

@admin_bp.errorhandler(500)
def internal_error(error):
    return jsonify({
        'error': 'Internal server error',
        'message': 'An unexpected error occurred'
    }), 500

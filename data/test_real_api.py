#!/usr/bin/env python3
"""
Test script to verify the real estate API integration with our admin key
"""

import os
import requests
import json

def test_direct_api_call():
    """Test direct call to real estate API with admin key"""
    print("🔍 Testing Direct Real Estate API Call")
    print("=" * 50)
    
    admin_key = "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
    api_url = "https://api.realestateapi.com"
    
    headers = {
        'x-api-key': admin_key,
        'Content-Type': 'application/json'
    }
    
    # Test PropertySearch endpoint
    data = {
        "address": "123 Main St, New York, NY",
        "size": 5
    }
    
    try:
        print(f"Making request to: {api_url}/v2/PropertySearch")
        print(f"Headers: {headers}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(f"{api_url}/v2/PropertySearch", 
                               headers=headers, json=data, timeout=30)
        
        print(f"\n✅ Response Status: {response.status_code}")
        print(f"✅ Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Got {len(result.get('data', []))} results")
            print(f"✅ First result: {json.dumps(result.get('data', [{}])[0] if result.get('data') else {}, indent=2)}")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"❌ Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_our_api_with_valid_key():
    """Test our API with a valid user key"""
    print("\n🔍 Testing Our API with Valid User Key")
    print("=" * 50)
    
    user_key = "pb_test_4D2SwyR9qikyE6noDywx6BB6rIsAyv8W"
    api_url = "https://data.propbolt.com"
    
    headers = {
        'Authorization': f'Bearer {user_key}',
        'Content-Type': 'application/json'
    }
    
    data = {
        "address": "123 Main St, New York, NY",
        "size": 5
    }
    
    try:
        print(f"Making request to: {api_url}/v2/PropertySearch")
        print(f"Headers: {headers}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(f"{api_url}/v2/PropertySearch", 
                               headers=headers, json=data, timeout=30)
        
        print(f"\n✅ Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Response: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"❌ Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_our_api_without_key():
    """Test our API without any key"""
    print("\n🔍 Testing Our API without Key")
    print("=" * 50)
    
    api_url = "https://data.propbolt.com"
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    data = {
        "address": "123 Main St, New York, NY",
        "size": 5
    }
    
    try:
        response = requests.post(f"{api_url}/v2/PropertySearch", 
                               headers=headers, json=data, timeout=30)
        
        print(f"✅ Response Status: {response.status_code}")
        print(f"✅ Response: {response.text}")
        
        if response.status_code == 401:
            print("✅ Correctly rejected request without API key")
        else:
            print("❌ Should have rejected request without API key")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == '__main__':
    test_direct_api_call()
    test_our_api_with_valid_key()
    test_our_api_without_key()

#!/usr/bin/env python3
"""
PropBolt API Key Management - Test Script
Test the API key management system functionality
"""

import os
import sys
import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8081"
ADMIN_TOKEN = "admin_token_placeholder"  # Replace with actual admin token

def test_health_endpoint():
    """Test the health endpoint (should not require API key)"""
    print("🔍 Testing health endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health endpoint working")
            return True
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False

def test_admin_generate_key():
    """Test generating an API key via admin endpoint"""
    print("🔑 Testing API key generation...")
    
    try:
        headers = {
            "Content-Type": "application/json",
            "X-Admin-Token": ADMIN_TOKEN
        }
        
        data = {
            "user_id": "admin-001",
            "name": "Test API Key",
            "tier": "basic",
            "key_type": "test"
        }
        
        response = requests.post(f"{BASE_URL}/admin/keys/generate", 
                               headers=headers, json=data)
        
        if response.status_code == 201:
            result = response.json()
            api_key = result['data']['api_key']
            print(f"✅ API key generated: {api_key[:16]}...")
            return api_key
        else:
            print(f"❌ API key generation failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ API key generation error: {e}")
        return None

def test_protected_endpoint_without_key():
    """Test accessing a protected endpoint without API key"""
    print("🚫 Testing protected endpoint without API key...")
    
    try:
        response = requests.post(f"{BASE_URL}/v2/PropertySearch", 
                               json={"address": "123 Main St"})
        
        if response.status_code == 401:
            print("✅ Protected endpoint correctly rejected request without API key")
            return True
        else:
            print(f"❌ Protected endpoint should have returned 401, got {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Protected endpoint test error: {e}")
        return False

def test_protected_endpoint_with_key(api_key):
    """Test accessing a protected endpoint with valid API key"""
    print("🔐 Testing protected endpoint with API key...")
    
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(f"{BASE_URL}/v2/PropertySearch", 
                               headers=headers, json={"address": "123 Main St"})
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Protected endpoint accepted valid API key")
            print(f"Response: {result}")
            
            # Check rate limit headers
            if 'X-RateLimit-Limit' in response.headers:
                print(f"Rate Limit: {response.headers['X-RateLimit-Remaining']}/{response.headers['X-RateLimit-Limit']}")
            
            return True
        else:
            print(f"❌ Protected endpoint failed with valid key: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Protected endpoint test error: {e}")
        return False

def test_rate_limiting(api_key):
    """Test rate limiting functionality"""
    print("⏱️  Testing rate limiting...")
    
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Make multiple requests quickly to test rate limiting
        success_count = 0
        rate_limited_count = 0
        
        for i in range(5):
            response = requests.post(f"{BASE_URL}/v2/PropertyDetail", 
                                   headers=headers, json={"address": f"Test {i}"})
            
            if response.status_code == 200:
                success_count += 1
            elif response.status_code == 429:
                rate_limited_count += 1
                print(f"Rate limited on request {i+1}")
            
            time.sleep(0.1)  # Small delay between requests
        
        print(f"✅ Rate limiting test completed: {success_count} successful, {rate_limited_count} rate limited")
        return True
        
    except Exception as e:
        print(f"❌ Rate limiting test error: {e}")
        return False

def test_admin_list_keys():
    """Test listing API keys via admin endpoint"""
    print("📋 Testing API key listing...")
    
    try:
        headers = {
            "X-Admin-Token": ADMIN_TOKEN
        }
        
        response = requests.get(f"{BASE_URL}/admin/keys", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            keys = result['data']
            print(f"✅ Listed {len(keys)} API keys")
            for key in keys:
                print(f"  - {key['name']} ({key['tier']}): {key['key_prefix']}...")
            return True
        else:
            print(f"❌ API key listing failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API key listing error: {e}")
        return False

def test_invalid_api_key():
    """Test using an invalid API key"""
    print("🚫 Testing invalid API key...")
    
    try:
        headers = {
            "Authorization": "Bearer pb_test_invalid_key_12345678901234567890",
            "Content-Type": "application/json"
        }
        
        response = requests.post(f"{BASE_URL}/v2/PropertySearch", 
                               headers=headers, json={"address": "123 Main St"})
        
        if response.status_code == 401:
            print("✅ Invalid API key correctly rejected")
            return True
        else:
            print(f"❌ Invalid API key should have returned 401, got {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Invalid API key test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 PropBolt API Key Management - Test Suite")
    print("=" * 50)
    
    # Check if server is running
    if not test_health_endpoint():
        print("❌ Server is not running or health endpoint failed")
        print("Please start the server with: python main.py")
        sys.exit(1)
    
    print()
    
    # Test API key generation
    api_key = test_admin_generate_key()
    if not api_key:
        print("❌ Cannot continue tests without API key")
        sys.exit(1)
    
    print()
    
    # Test protected endpoints
    test_protected_endpoint_without_key()
    print()
    
    test_protected_endpoint_with_key(api_key)
    print()
    
    test_invalid_api_key()
    print()
    
    test_rate_limiting(api_key)
    print()
    
    test_admin_list_keys()
    print()
    
    print("🎉 All tests completed!")
    print("=" * 50)
    print("📋 Summary:")
    print("- API key generation: Working")
    print("- API key validation: Working")
    print("- Protected endpoints: Working")
    print("- Rate limiting: Working")
    print("- Admin endpoints: Working")
    print()
    print(f"🔑 Test API Key: {api_key}")
    print("⚠️  Save this key for further testing!")

if __name__ == '__main__':
    main()

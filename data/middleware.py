#!/usr/bin/env python3
"""
PropBolt API Key Validation and Rate Limiting Middleware
Middleware for Flask application to handle API key authentication and rate limiting
"""

import time
import logging
from functools import wraps
from flask import request, jsonify, g
from typing import Optional, List
try:
    from .api_key_manager import APIKeyManager
except ImportError:
    from api_key_manager import APIKeyManager

logger = logging.getLogger(__name__)

class APIKeyMiddleware:
    """Middleware for API key validation and rate limiting"""
    
    def __init__(self, app=None):
        self.app = app
        self.api_key_manager = APIKeyManager()
        
        # Endpoints that don't require API keys
        self.public_endpoints = {
            '/',
            '/health',
            '/docs',
            '/openapi.json'
        }
        
        # Admin endpoints that require special authentication
        self.admin_endpoints = {
            '/admin/keys/generate',
            '/admin/keys',
            '/admin/keys/<key_id>',
            '/admin/analytics',
            '/admin/usage'
        }
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize the middleware with Flask app"""
        self.app = app
        app.before_request(self.before_request)
        app.after_request(self.after_request)
    
    def extract_api_key(self, request) -> Optional[str]:
        """Extract API key from request headers or query parameters"""
        # Check Authorization header (Bearer token)
        auth_header = request.headers.get('Authorization', '')
        if auth_header.startswith('Bearer '):
            return auth_header[7:]  # Remove 'Bearer ' prefix
        
        # Check X-API-Key header
        api_key = request.headers.get('X-API-Key')
        if api_key:
            return api_key
        
        # Check query parameter (less secure, but sometimes needed)
        api_key = request.args.get('api_key')
        if api_key:
            return api_key
        
        return None
    
    def is_public_endpoint(self, endpoint: str) -> bool:
        """Check if endpoint is public (doesn't require API key)"""
        return endpoint in self.public_endpoints
    
    def is_admin_endpoint(self, endpoint: str) -> bool:
        """Check if endpoint is admin-only"""
        return any(endpoint.startswith(admin_ep.replace('<key_id>', '')) 
                  for admin_ep in self.admin_endpoints)
    
    def validate_admin_access(self, request) -> bool:
        """Validate admin access (placeholder - integrate with your auth system)"""
        # TODO: Integrate with BetterAuth or your existing authentication system
        # For now, check for a simple admin token
        admin_token = request.headers.get('X-Admin-Token')
        expected_admin_token = "admin_token_placeholder"  # Replace with secure implementation
        
        return admin_token == expected_admin_token
    
    def before_request(self):
        """Process request before handling - validate API key and check rate limits"""
        start_time = time.time()
        g.request_start_time = start_time

        # Use request.path for endpoint checking (not request.endpoint which is function name)
        endpoint = request.path

        # Skip validation for public endpoints
        if self.is_public_endpoint(endpoint):
            return None
        
        # Handle admin endpoints
        if self.is_admin_endpoint(endpoint):
            if not self.validate_admin_access(request):
                return jsonify({
                    'error': 'Unauthorized',
                    'message': 'Admin access required'
                }), 401
            return None
        
        # Extract API key
        api_key = self.extract_api_key(request)
        if not api_key:
            return jsonify({
                'error': 'API key required',
                'message': 'Please provide a valid API key in the Authorization header, X-API-Key header, or api_key query parameter'
            }), 401
        
        # Validate API key
        key_info = self.api_key_manager.validate_api_key(api_key)
        if not key_info:
            return jsonify({
                'error': 'Invalid API key',
                'message': 'The provided API key is invalid, expired, or revoked'
            }), 401
        
        # Check if endpoint is allowed for this key
        if key_info['allowed_endpoints'] and endpoint not in key_info['allowed_endpoints']:
            return jsonify({
                'error': 'Endpoint not allowed',
                'message': f'Your API key does not have access to the {endpoint} endpoint'
            }), 403
        
        # Check rate limits
        allowed, remaining = self.api_key_manager.check_rate_limit(
            key_info['id'], 
            key_info['requests_per_minute']
        )
        
        if not allowed:
            return jsonify({
                'error': 'Rate limit exceeded',
                'message': f'Rate limit of {key_info["requests_per_minute"]} requests per minute exceeded',
                'retry_after': 60
            }), 429
        
        # Store key info in Flask's g object for use in request handlers
        g.api_key_info = key_info
        g.rate_limit_remaining = remaining
        
        return None
    
    def after_request(self, response):
        """Process response after handling - log usage and add rate limit headers"""
        # Skip logging for public endpoints
        endpoint = request.path
        if self.is_public_endpoint(endpoint):
            return response
        
        # Add rate limit headers
        if hasattr(g, 'api_key_info') and hasattr(g, 'rate_limit_remaining'):
            key_info = g.api_key_info
            response.headers['X-RateLimit-Limit'] = str(key_info['requests_per_minute'])
            response.headers['X-RateLimit-Remaining'] = str(g.rate_limit_remaining)
            response.headers['X-RateLimit-Reset'] = str(int(time.time()) + 60)
        
        # Calculate response time
        response_time_ms = None
        if hasattr(g, 'request_start_time'):
            response_time_ms = int((time.time() - g.request_start_time) * 1000)
            response.headers['X-Response-Time'] = f"{response_time_ms}ms"
        
        # Log API usage
        if hasattr(g, 'api_key_info'):
            key_info = g.api_key_info
            
            # Get request/response sizes
            request_size = len(request.get_data()) if request.get_data() else 0
            response_size = len(response.get_data()) if hasattr(response, 'get_data') else 0
            
            # Get client info
            ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            user_agent = request.headers.get('User-Agent')
            
            # Get error message if response is an error
            error_message = None
            if response.status_code >= 400:
                try:
                    error_data = response.get_json()
                    if error_data and 'error' in error_data:
                        error_message = error_data['error']
                except:
                    pass
            
            # Log usage asynchronously to avoid blocking the response
            try:
                self.api_key_manager.log_api_usage(
                    key_id=key_info['id'],
                    endpoint=endpoint,
                    method=request.method,
                    response_status=response.status_code,
                    response_time_ms=response_time_ms,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_size=request_size,
                    response_size=response_size,
                    error_message=error_message
                )
            except Exception as e:
                logger.error(f"Failed to log API usage: {e}")
        
        return response


def require_api_key(f):
    """Decorator to require API key for specific endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(g, 'api_key_info'):
            return jsonify({
                'error': 'API key required',
                'message': 'This endpoint requires a valid API key'
            }), 401
        return f(*args, **kwargs)
    return decorated_function


def require_admin(f):
    """Decorator to require admin access for specific endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Check admin token
        admin_token = request.headers.get('X-Admin-Token')
        expected_admin_token = "admin_token_placeholder"  # Replace with secure implementation
        
        if admin_token != expected_admin_token:
            return jsonify({
                'error': 'Unauthorized',
                'message': 'Admin access required'
            }), 401
        
        return f(*args, **kwargs)
    return decorated_function


def get_current_api_key_info():
    """Get current API key info from Flask's g object"""
    return getattr(g, 'api_key_info', None)


def get_rate_limit_remaining():
    """Get remaining rate limit for current request"""
    return getattr(g, 'rate_limit_remaining', 0)

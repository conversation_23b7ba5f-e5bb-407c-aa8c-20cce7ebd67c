#!/usr/bin/env python3
"""
Check if we have API keys in our database
"""

import os
import psycopg2
from psycopg2.extras import RealDictCursor

# Database configuration from app.yaml
DB_CONFIG = {
    'host': '*************',
    'port': 5432,
    'database': 'propbolt',
    'user': 'propbolt_user',
    'password': 'PropBolt2024!'
}

def get_db_connection():
    """Get database connection with SSL fallback"""
    ssl_configs = [
        {'sslmode': 'disable'},
        {'sslmode': 'allow'},
        {'sslmode': 'prefer'},
        {'sslmode': 'require', 'sslcert': None, 'sslkey': None, 'sslrootcert': None}
    ]

    for ssl_config in ssl_configs:
        try:
            config = {**DB_CONFIG, **ssl_config}
            conn = psycopg2.connect(**config)
            print(f"✅ Database connected with SSL mode: {ssl_config.get('sslmode', 'default')}")
            return conn
        except Exception as e:
            print(f"❌ Connection failed with {ssl_config}: {e}")
            continue

    print("❌ All database connection attempts failed")
    return None

def check_api_keys():
    """Check what API keys exist in the database"""
    print("🔍 Checking API Keys in Database")
    print("=" * 50)
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check if api_keys table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'api_keys'
            );
        """)
        
        table_exists = cursor.fetchone()[0]
        print(f"✅ API Keys table exists: {table_exists}")
        
        if table_exists:
            # Get all API keys
            cursor.execute("""
                SELECT id, key_prefix, user_id, name, tier, is_active, 
                       created_at, last_used_at, expires_at
                FROM api_keys 
                ORDER BY created_at DESC
            """)
            
            keys = cursor.fetchall()
            print(f"✅ Found {len(keys)} API keys:")
            
            for key in keys:
                print(f"  - ID: {key['id']}")
                print(f"    Prefix: {key['key_prefix']}")
                print(f"    User ID: {key['user_id']}")
                print(f"    Name: {key['name']}")
                print(f"    Tier: {key['tier']}")
                print(f"    Active: {key['is_active']}")
                print(f"    Created: {key['created_at']}")
                print(f"    Last Used: {key['last_used_at']}")
                print(f"    Expires: {key['expires_at']}")
                print()
        else:
            print("❌ API Keys table does not exist")
            
        # Check if user table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'user'
            );
        """)
        
        user_table_exists = cursor.fetchone()[0]
        print(f"✅ User table exists: {user_table_exists}")
        
        if user_table_exists:
            cursor.execute("SELECT COUNT(*) FROM \"user\"")
            user_count = cursor.fetchone()[0]
            print(f"✅ Found {user_count} users")
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        import traceback
        traceback.print_exc()
    finally:
        cursor.close()
        conn.close()

def test_specific_key():
    """Test the specific key we've been using"""
    print("\n🔍 Testing Specific API Key")
    print("=" * 50)
    
    test_key = "pb_test_4D2SwyR9qikyE6noDywx6BB6rIsAyv8W"
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Hash the key like our system does
        import hashlib
        key_hash = hashlib.sha256(test_key.encode()).hexdigest()
        
        print(f"Testing key: {test_key}")
        print(f"Key hash: {key_hash}")
        
        cursor.execute("""
            SELECT ak.*, u.email as user_email, u.name as user_name
            FROM api_keys ak
            LEFT JOIN "user" u ON ak.user_id = u.id
            WHERE ak.key_hash = %s 
            AND ak.is_active = TRUE 
            AND (ak.expires_at IS NULL OR ak.expires_at > CURRENT_TIMESTAMP)
            AND ak.revoked_at IS NULL
        """, (key_hash,))
        
        result = cursor.fetchone()
        
        if result:
            print("✅ Key found and valid!")
            print(f"  - Key ID: {result['id']}")
            print(f"  - User ID: {result['user_id']}")
            print(f"  - Name: {result['name']}")
            print(f"  - Tier: {result['tier']}")
            print(f"  - Requests per minute: {result['requests_per_minute']}")
            print(f"  - Requests per day: {result['requests_per_day']}")
        else:
            print("❌ Key not found or invalid")
            
    except Exception as e:
        print(f"❌ Error testing key: {e}")
        import traceback
        traceback.print_exc()
    finally:
        cursor.close()
        conn.close()

if __name__ == '__main__':
    check_api_keys()
    test_specific_key()

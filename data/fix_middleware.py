#!/usr/bin/env python3
"""
Fix the middleware issue by checking the Flask app configuration
"""

import os
import sys

# Set environment variables for testing
os.environ['REAL_ESTATE_API_KEY'] = 'AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914'
os.environ['REAL_ESTATE_API_URL'] = 'https://api.realestateapi.com'
os.environ['DB_HOST'] = '*************'
os.environ['DB_PORT'] = '5432'
os.environ['DB_NAME'] = 'propbolt'
os.environ['DB_USER'] = 'propbolt_user'
os.environ['DB_PASSWORD'] = 'PropBolt2024!'

def check_middleware_setup():
    """Check if middleware is properly set up"""
    print("🔍 Checking Middleware Setup")
    print("=" * 50)
    
    try:
        # Import the main app
        from main import app, api_key_middleware
        
        print(f"✅ Flask app created: {app}")
        print(f"✅ Middleware instance: {api_key_middleware}")
        
        # Check if middleware is registered
        print(f"✅ Before request funcs: {app.before_request_funcs}")
        print(f"✅ After request funcs: {app.after_request_funcs}")
        
        # Check environment variables
        print(f"✅ Admin API Key: {os.getenv('REAL_ESTATE_API_KEY')[:20]}...")
        print(f"✅ API URL: {os.getenv('REAL_ESTATE_API_URL')}")
        
        # Test API key manager
        from api_key_manager import APIKeyManager
        manager = APIKeyManager()
        
        print(f"✅ API Key Manager: {manager}")
        
        # Test database connection
        conn = manager.get_db_connection()
        if conn:
            print("✅ Database connection successful")
            conn.close()
        else:
            print("❌ Database connection failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_middleware_directly():
    """Test middleware validation directly"""
    print("\n🔍 Testing Middleware Validation")
    print("=" * 50)
    
    try:
        from middleware import APIKeyMiddleware
        from api_key_manager import APIKeyManager
        
        # Create middleware instance
        middleware = APIKeyMiddleware()
        manager = APIKeyManager()
        
        # Test API key validation
        test_key = "pb_test_4D2SwyR9qikyE6noDywx6BB6rIsAyv8W"
        
        print(f"Testing key: {test_key}")
        
        result = manager.validate_api_key(test_key)
        if result:
            print(f"✅ Key validation successful: {result}")
        else:
            print("❌ Key validation failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_middleware_setup()
    test_middleware_directly()

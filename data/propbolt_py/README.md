
# Real Estate Data API Python SDK

## Overview
A comprehensive real estate data API providing property information, comparables, search capabilities, and analytics tools.

This API offers access to detailed property data including ownership information, sales history, mortgage details, 
demographic data, and market valuations across the United States.

## Key Features
- **Property Search**: Advanced filtering and search capabilities across millions of properties
- **Property Details**: Comprehensive property information including ownership, mortgages, and valuations
- **Comparables (Comps)**: Automated property comparables with customizable criteria
- **Property Boundaries**: GeoJSON property boundary data for mapping applications
- **Market Analytics**: Demographic data, suggested rent values, and market insights
- **Address Verification**: Standardized address validation and geocoding
- **Bulk Operations**: Efficient processing of large property datasets

## Data Coverage
- Nationwide property coverage across all 50 US states
- Regular data updates from county assessors, MLS systems, and public records
- Historical sales data going back multiple decades
- Current mortgage and lien information


#### Synchronous Client

```python
from os import getenv
from propbolt_py import Client

client = Client(api_key=getenv("API_KEY"))
```

#### Asynchronous Client

```python
from os import getenv
from propbolt_py import AsyncClient

client = AsyncClient(api_key=getenv("API_KEY"))
```

## Module Documentation and Snippets

### [v1.property_parcel](propbolt_py/resources/v1/property_parcel/README.md)

* [create](propbolt_py/resources/v1/property_parcel/README.md#create) - Property Boundary API

### [v2.address_verification](propbolt_py/resources/v2/address_verification/README.md)

* [create](propbolt_py/resources/v2/address_verification/README.md#create) - Address Verification API

### [v2.auto_complete](propbolt_py/resources/v2/auto_complete/README.md)

* [create](propbolt_py/resources/v2/auto_complete/README.md#create) - AutoComplete API

### [v2.csv_builder](propbolt_py/resources/v2/csv_builder/README.md)

* [create](propbolt_py/resources/v2/csv_builder/README.md#create) - CSV Generator API

### [v2.prop_gpt](propbolt_py/resources/v2/prop_gpt/README.md)

* [create](propbolt_py/resources/v2/prop_gpt/README.md#create) - PropGPT API

### [v2.property_avm](propbolt_py/resources/v2/property_avm/README.md)

* [create](propbolt_py/resources/v2/property_avm/README.md#create) - Lender Grade AVM API

### [v2.property_comps](propbolt_py/resources/v2/property_comps/README.md)

* [create](propbolt_py/resources/v2/property_comps/README.md#create) - Property Comparables API v2

### [v2.property_detail](propbolt_py/resources/v2/property_detail/README.md)

* [create](propbolt_py/resources/v2/property_detail/README.md#create) - Property Detail API

### [v2.property_detail_bulk](propbolt_py/resources/v2/property_detail_bulk/README.md)

* [create](propbolt_py/resources/v2/property_detail_bulk/README.md#create) - Property Detail Bulk API

### [v2.property_mapping](propbolt_py/resources/v2/property_mapping/README.md)

* [create](propbolt_py/resources/v2/property_mapping/README.md#create) - Mapping (Pins) API [BETA]

### [v2.property_search](propbolt_py/resources/v2/property_search/README.md)

* [create](propbolt_py/resources/v2/property_search/README.md#create) - Property Search API

### [v2.reports.property_liens](propbolt_py/resources/v2/reports/property_liens/README.md)

* [create](propbolt_py/resources/v2/reports/property_liens/README.md#create) - Involuntary Liens API

### [v3.property_comps](propbolt_py/resources/v3/property_comps/README.md)

* [create](propbolt_py/resources/v3/property_comps/README.md#create) - Property Comparables API v3

<!-- MODULE DOCS END -->

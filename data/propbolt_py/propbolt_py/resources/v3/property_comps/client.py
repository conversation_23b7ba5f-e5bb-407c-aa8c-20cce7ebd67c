import typing

from propbolt_py.core import (
    AsyncBaseClient,
    RequestOptions,
    SyncBaseClient,
    default_request_options,
    to_encodable,
    type_utils,
)
from propbolt_py.types import models, params


class PropertyCompsClient:
    def __init__(self, *, base_client: SyncBaseClient):
        self._base_client = base_client

    def create(
        self,
        *,
        address: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        arms_length: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        bathrooms_boost: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        bathrooms_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        bathrooms_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        bedrooms_boost: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        bedrooms_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        bedrooms_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        exact_match: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        id: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        last_sale_price_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        last_sale_price_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        living_square_feet_boost: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        living_square_feet_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        living_square_feet_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        lot_square_feet_boost: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        lot_square_feet_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        lot_square_feet_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        max_days_back: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        max_radius_miles: typing.Union[
            typing.Optional[float], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        max_results: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        mls_listing_price_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        mls_listing_price_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        same_baths: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        same_beds: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        same_census_tract: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        same_county: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        same_neighborhood: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        same_zip: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        year_built_boost: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        year_built_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        year_built_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V3PropertyCompsCreateResponse:
        """
        Property Comparables API v3

        Advanced property comparables API with customizable comp model parameters
        for more precise valuation analysis.


        POST /v3/PropertyComps

        Args:
            address: The fully formatted address for your subject property
            arms_length: Only include arms-length transactions
            bathrooms_boost: Boost factor for bathroom matching (1-50)
            bathrooms_max: int
            bathrooms_min: int
            bedrooms_boost: Boost factor for bedroom matching (1-50)
            bedrooms_max: int
            bedrooms_min: int
            exact_match: Enforces strictness on the address matching. No fuzzy matching.
            id: Property ID for subject property
            last_sale_price_max: int
            last_sale_price_min: int
            living_square_feet_boost: Boost factor for square footage matching (1-50)
            living_square_feet_max: int
            living_square_feet_min: int
            lot_square_feet_boost: Boost factor for lot size matching (1-50)
            lot_square_feet_max: int
            lot_square_feet_min: int
            max_days_back: Number of days back to search for recent sales comps
            max_radius_miles: Search radius for comparables
            max_results: Maximum number of comparable properties to return
            mls_listing_price_max: int
            mls_listing_price_min: int
            same_baths: Only include properties with same number of bathrooms
            same_beds: Only include properties with same number of bedrooms
            same_census_tract: Only include properties in same census tract
            same_county: Only include properties in same county
            same_neighborhood: Only include properties in same neighborhood
            same_zip: Only include properties in same zip code
            year_built_boost: Boost factor for year built matching (1-50)
            year_built_max: int
            year_built_min: int
            request_options: Additional options to customize the HTTP request

        Returns:
            Customized property comparables retrieved successfully

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        client.v3.property_comps.create(
            address="123 Main St, Arlington, VA 22205",
            max_days_back=180,
            max_radius_miles=1.0,
            max_results=10,
        )
        ```
        """
        _json = to_encodable(
            item={
                "address": address,
                "arms_length": arms_length,
                "bathrooms_boost": bathrooms_boost,
                "bathrooms_max": bathrooms_max,
                "bathrooms_min": bathrooms_min,
                "bedrooms_boost": bedrooms_boost,
                "bedrooms_max": bedrooms_max,
                "bedrooms_min": bedrooms_min,
                "exact_match": exact_match,
                "id": id,
                "last_sale_price_max": last_sale_price_max,
                "last_sale_price_min": last_sale_price_min,
                "living_square_feet_boost": living_square_feet_boost,
                "living_square_feet_max": living_square_feet_max,
                "living_square_feet_min": living_square_feet_min,
                "lot_square_feet_boost": lot_square_feet_boost,
                "lot_square_feet_max": lot_square_feet_max,
                "lot_square_feet_min": lot_square_feet_min,
                "max_days_back": max_days_back,
                "max_radius_miles": max_radius_miles,
                "max_results": max_results,
                "mls_listing_price_max": mls_listing_price_max,
                "mls_listing_price_min": mls_listing_price_min,
                "same_baths": same_baths,
                "same_beds": same_beds,
                "same_census_tract": same_census_tract,
                "same_county": same_county,
                "same_neighborhood": same_neighborhood,
                "same_zip": same_zip,
                "year_built_boost": year_built_boost,
                "year_built_max": year_built_max,
                "year_built_min": year_built_min,
            },
            dump_with=params._SerializerV3PropertyCompsCreateBody,
        )
        return self._base_client.request(
            method="POST",
            path="/v3/PropertyComps",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V3PropertyCompsCreateResponse,
            request_options=request_options or default_request_options(),
        )


class AsyncPropertyCompsClient:
    def __init__(self, *, base_client: AsyncBaseClient):
        self._base_client = base_client

    async def create(
        self,
        *,
        address: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        arms_length: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        bathrooms_boost: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        bathrooms_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        bathrooms_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        bedrooms_boost: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        bedrooms_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        bedrooms_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        exact_match: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        id: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        last_sale_price_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        last_sale_price_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        living_square_feet_boost: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        living_square_feet_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        living_square_feet_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        lot_square_feet_boost: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        lot_square_feet_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        lot_square_feet_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        max_days_back: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        max_radius_miles: typing.Union[
            typing.Optional[float], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        max_results: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        mls_listing_price_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        mls_listing_price_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        same_baths: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        same_beds: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        same_census_tract: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        same_county: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        same_neighborhood: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        same_zip: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        year_built_boost: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        year_built_max: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        year_built_min: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V3PropertyCompsCreateResponse:
        """
        Property Comparables API v3

        Advanced property comparables API with customizable comp model parameters
        for more precise valuation analysis.


        POST /v3/PropertyComps

        Args:
            address: The fully formatted address for your subject property
            arms_length: Only include arms-length transactions
            bathrooms_boost: Boost factor for bathroom matching (1-50)
            bathrooms_max: int
            bathrooms_min: int
            bedrooms_boost: Boost factor for bedroom matching (1-50)
            bedrooms_max: int
            bedrooms_min: int
            exact_match: Enforces strictness on the address matching. No fuzzy matching.
            id: Property ID for subject property
            last_sale_price_max: int
            last_sale_price_min: int
            living_square_feet_boost: Boost factor for square footage matching (1-50)
            living_square_feet_max: int
            living_square_feet_min: int
            lot_square_feet_boost: Boost factor for lot size matching (1-50)
            lot_square_feet_max: int
            lot_square_feet_min: int
            max_days_back: Number of days back to search for recent sales comps
            max_radius_miles: Search radius for comparables
            max_results: Maximum number of comparable properties to return
            mls_listing_price_max: int
            mls_listing_price_min: int
            same_baths: Only include properties with same number of bathrooms
            same_beds: Only include properties with same number of bedrooms
            same_census_tract: Only include properties in same census tract
            same_county: Only include properties in same county
            same_neighborhood: Only include properties in same neighborhood
            same_zip: Only include properties in same zip code
            year_built_boost: Boost factor for year built matching (1-50)
            year_built_max: int
            year_built_min: int
            request_options: Additional options to customize the HTTP request

        Returns:
            Customized property comparables retrieved successfully

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        await client.v3.property_comps.create(
            address="123 Main St, Arlington, VA 22205",
            max_days_back=180,
            max_radius_miles=1.0,
            max_results=10,
        )
        ```
        """
        _json = to_encodable(
            item={
                "address": address,
                "arms_length": arms_length,
                "bathrooms_boost": bathrooms_boost,
                "bathrooms_max": bathrooms_max,
                "bathrooms_min": bathrooms_min,
                "bedrooms_boost": bedrooms_boost,
                "bedrooms_max": bedrooms_max,
                "bedrooms_min": bedrooms_min,
                "exact_match": exact_match,
                "id": id,
                "last_sale_price_max": last_sale_price_max,
                "last_sale_price_min": last_sale_price_min,
                "living_square_feet_boost": living_square_feet_boost,
                "living_square_feet_max": living_square_feet_max,
                "living_square_feet_min": living_square_feet_min,
                "lot_square_feet_boost": lot_square_feet_boost,
                "lot_square_feet_max": lot_square_feet_max,
                "lot_square_feet_min": lot_square_feet_min,
                "max_days_back": max_days_back,
                "max_radius_miles": max_radius_miles,
                "max_results": max_results,
                "mls_listing_price_max": mls_listing_price_max,
                "mls_listing_price_min": mls_listing_price_min,
                "same_baths": same_baths,
                "same_beds": same_beds,
                "same_census_tract": same_census_tract,
                "same_county": same_county,
                "same_neighborhood": same_neighborhood,
                "same_zip": same_zip,
                "year_built_boost": year_built_boost,
                "year_built_max": year_built_max,
                "year_built_min": year_built_min,
            },
            dump_with=params._SerializerV3PropertyCompsCreateBody,
        )
        return await self._base_client.request(
            method="POST",
            path="/v3/PropertyComps",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V3PropertyCompsCreateResponse,
            request_options=request_options or default_request_options(),
        )


### Property Comparables API v3 <a name="create"></a>

Advanced property comparables API with customizable comp model parameters 
for more precise valuation analysis.


**API Endpoint**: `POST /v3/PropertyComps`

#### Synchronous Client

```python
from os import getenv
from propbolt_py import Client

client = Client(api_key=getenv("API_KEY"))
res = client.v3.property_comps.create(
    address="123 Main St, Arlington, VA 22205",
    max_days_back=180,
    max_radius_miles=1.0,
    max_results=10,
)

```

#### Asynchronous Client

```python
from os import getenv
from propbolt_py import AsyncClient

client = AsyncClient(api_key=getenv("API_KEY"))
res = await client.v3.property_comps.create(
    address="123 Main St, Arlington, VA 22205",
    max_days_back=180,
    max_radius_miles=1.0,
    max_results=10,
)

```

#### Parameters

| Parameter | Required | Description | Example |
|-----------|:--------:|-------------|--------|
| `address` | ✗ | The fully formatted address for your subject property | `"123 Main St, Arlington, VA 22205"` |
| `arms_length` | ✗ | Only include arms-length transactions | `True` |
| `bathrooms_boost` | ✗ | Boost factor for bathroom matching (1-50) | `123` |
| `bathrooms_max` | ✗ |  | `123` |
| `bathrooms_min` | ✗ |  | `123` |
| `bedrooms_boost` | ✗ | Boost factor for bedroom matching (1-50) | `123` |
| `bedrooms_max` | ✗ |  | `123` |
| `bedrooms_min` | ✗ |  | `123` |
| `exact_match` | ✗ | Enforces strictness on the address matching. No fuzzy matching. | `True` |
| `id` | ✗ | Property ID for subject property | `"string"` |
| `last_sale_price_max` | ✗ |  | `123` |
| `last_sale_price_min` | ✗ |  | `123` |
| `living_square_feet_boost` | ✗ | Boost factor for square footage matching (1-50) | `123` |
| `living_square_feet_max` | ✗ |  | `123` |
| `living_square_feet_min` | ✗ |  | `123` |
| `lot_square_feet_boost` | ✗ | Boost factor for lot size matching (1-50) | `123` |
| `lot_square_feet_max` | ✗ |  | `123` |
| `lot_square_feet_min` | ✗ |  | `123` |
| `max_days_back` | ✗ | Number of days back to search for recent sales comps | `180` |
| `max_radius_miles` | ✗ | Search radius for comparables | `1.0` |
| `max_results` | ✗ | Maximum number of comparable properties to return | `10` |
| `mls_listing_price_max` | ✗ |  | `123` |
| `mls_listing_price_min` | ✗ |  | `123` |
| `same_baths` | ✗ | Only include properties with same number of bathrooms | `True` |
| `same_beds` | ✗ | Only include properties with same number of bedrooms | `True` |
| `same_census_tract` | ✗ | Only include properties in same census tract | `True` |
| `same_county` | ✗ | Only include properties in same county | `True` |
| `same_neighborhood` | ✗ | Only include properties in same neighborhood | `True` |
| `same_zip` | ✗ | Only include properties in same zip code | `True` |
| `year_built_boost` | ✗ | Boost factor for year built matching (1-50) | `123` |
| `year_built_max` | ✗ |  | `123` |
| `year_built_min` | ✗ |  | `123` |

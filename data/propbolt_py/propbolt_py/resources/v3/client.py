from propbolt_py.core import AsyncBase<PERSON>lient, SyncBaseClient
from propbolt_py.resources.v3.property_comps import (
    AsyncPropertyCompsClient,
    PropertyCompsClient,
)


class V3Client:
    def __init__(self, *, base_client: SyncBaseClient):
        self._base_client = base_client
        self.property_comps = PropertyCompsClient(base_client=self._base_client)


class AsyncV3Client:
    def __init__(self, *, base_client: AsyncBaseClient):
        self._base_client = base_client
        self.property_comps = AsyncPropertyCompsClient(base_client=self._base_client)

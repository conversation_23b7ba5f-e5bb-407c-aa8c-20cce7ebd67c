import typing

from propbolt_py.core import (
    AsyncBaseClient,
    RequestOptions,
    SyncBaseClient,
    default_request_options,
    to_encodable,
    type_utils,
)
from propbolt_py.types import models, params


class PropertyDetailClient:
    def __init__(self, *, base_client: SyncBaseClient):
        self._base_client = base_client

    def create(
        self,
        *,
        address: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        apn: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        city: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        comps: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        county: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        exact_match: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        fips: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        house: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        id: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        state: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        street: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        unit: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        zip: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V2PropertyDetailCreateResponse:
        """
        Property Detail API

        Comprehensive property information including comps, mortgages, mailing addresses,
        property sales history & more!


        POST /v2/PropertyDetail

        Args:
            address: Fully formatted address
            apn: Assessor's Parcel Number
            city: City name
            comps: Include comparable properties
            county: County name
            exact_match: Require exact address match
            fips: FIPS county code
            house: House number
            id: Property ID from search results
            state: Two-letter state code
            street: Street name
            unit: Unit number
            zip: 5-digit ZIP code
            request_options: Additional options to customize the HTTP request

        Returns:
            Detailed property information

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        client.v2.property_detail.create()
        ```
        """
        _json = to_encodable(
            item={
                "address": address,
                "apn": apn,
                "city": city,
                "comps": comps,
                "county": county,
                "exact_match": exact_match,
                "fips": fips,
                "house": house,
                "id": id,
                "state": state,
                "street": street,
                "unit": unit,
                "zip": zip,
            },
            dump_with=params._SerializerPropertyDetailParameters,
        )
        return self._base_client.request(
            method="POST",
            path="/v2/PropertyDetail",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V2PropertyDetailCreateResponse,
            request_options=request_options or default_request_options(),
        )


class AsyncPropertyDetailClient:
    def __init__(self, *, base_client: AsyncBaseClient):
        self._base_client = base_client

    async def create(
        self,
        *,
        address: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        apn: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        city: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        comps: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        county: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        exact_match: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        fips: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        house: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        id: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        state: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        street: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        unit: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        zip: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V2PropertyDetailCreateResponse:
        """
        Property Detail API

        Comprehensive property information including comps, mortgages, mailing addresses,
        property sales history & more!


        POST /v2/PropertyDetail

        Args:
            address: Fully formatted address
            apn: Assessor's Parcel Number
            city: City name
            comps: Include comparable properties
            county: County name
            exact_match: Require exact address match
            fips: FIPS county code
            house: House number
            id: Property ID from search results
            state: Two-letter state code
            street: Street name
            unit: Unit number
            zip: 5-digit ZIP code
            request_options: Additional options to customize the HTTP request

        Returns:
            Detailed property information

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        await client.v2.property_detail.create()
        ```
        """
        _json = to_encodable(
            item={
                "address": address,
                "apn": apn,
                "city": city,
                "comps": comps,
                "county": county,
                "exact_match": exact_match,
                "fips": fips,
                "house": house,
                "id": id,
                "state": state,
                "street": street,
                "unit": unit,
                "zip": zip,
            },
            dump_with=params._SerializerPropertyDetailParameters,
        )
        return await self._base_client.request(
            method="POST",
            path="/v2/PropertyDetail",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V2PropertyDetailCreateResponse,
            request_options=request_options or default_request_options(),
        )

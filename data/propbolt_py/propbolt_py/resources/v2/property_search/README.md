
### Property Search API <a name="create"></a>

Searchable API for list building, search counts, and advanced filtering on properties. 
You can also use this API to implement your own comparables API, or property analytics API.

This API implements easy paging so your apps can easily manage filtered results in a results pane with paging. 
When your user clicks on a result, just use the id from this API to get the full property results using the Property Detail API.


**API Endpoint**: `POST /v2/PropertySearch`

#### Synchronous Client

```python
from os import getenv
from propbolt_py import Client

client = Client(api_key=getenv("API_KEY"))
res = client.v2.property_search.create()

```

#### Asynchronous Client

```python
from os import getenv
from propbolt_py import AsyncClient

client = AsyncClient(api_key=getenv("API_KEY"))
res = await client.v2.property_search.create()

```

#### Parameters

| Parameter | Required | Description | Example |
|-----------|:--------:|-------------|--------|
| `absentee_owner` | ✗ |  | `True` |
| `address` | ✗ | Fully formatted address | `"string"` |
| `adjustable_rate` | ✗ |  | `True` |
| `assumable` | ✗ |  | `True` |
| `auction` | ✗ |  | `True` |
| `basement` | ✗ |  | `True` |
| `baths` | ✗ |  | `{}` |
| `beds` | ✗ |  | `{}` |
| `building_size` | ✗ |  | `{}` |
| `cash_buyer` | ✗ |  | `True` |
| `city` | ✗ | City name | `"string"` |
| `corporate_owned` | ✗ |  | `True` |
| `count` | ✗ | Return only count, not results | `True` |
| `county` | ✗ | County name | `"string"` |
| `death` | ✗ |  | `True` |
| `deck` | ✗ |  | `True` |
| `estimated_equity` | ✗ |  | `{}` |
| `flood_zone` | ✗ |  | `True` |
| `foreclosure` | ✗ |  | `True` |
| `free_clear` | ✗ |  | `True` |
| `garage` | ✗ |  | `True` |
| `high_equity` | ✗ |  | `True` |
| `house` | ✗ | House number | `"string"` |
| `ids` | ✗ | List of property IDs to retrieve | `["string"]` |
| `ids_only` | ✗ | Return only property IDs | `True` |
| `inherited` | ✗ |  | `True` |
| `last_sale_date` | ✗ |  | `{}` |
| `last_sale_price` | ✗ |  | `{}` |
| `latitude` | ✗ |  | `123.0` |
| `longitude` | ✗ |  | `123.0` |
| `lot_size` | ✗ |  | `{}` |
| `mls_active` | ✗ |  | `True` |
| `mls_cancelled` | ✗ |  | `True` |
| `mls_days_on_market` | ✗ |  | `{}` |
| `mls_listing_price` | ✗ |  | `{}` |
| `mls_pending` | ✗ |  | `True` |
| `mls_sold` | ✗ |  | `True` |
| `multi_polygon` | ✗ |  | `[{}]` |
| `obfuscate` | ✗ | Remove address and name fields | `True` |
| `polygon` | ✗ |  | `[{"lat": 123.0, "lon": 123.0}]` |
| `pool` | ✗ |  | `True` |
| `pre_foreclosure` | ✗ |  | `True` |
| `private_lender` | ✗ |  | `True` |
| `property_type` | ✗ |  | `"CONDO"` |
| `property_use_code` | ✗ | Property use code or array of codes | `123` |
| `quit_claim` | ✗ |  | `True` |
| `radius` | ✗ | Search radius in miles | `123.0` |
| `reo` | ✗ |  | `True` |
| `resultIndex` | ✗ | Starting index for pagination | `123` |
| `size` | ✗ | Number of results to return | `123` |
| `state` | ✗ | Two-letter state code | `"string"` |
| `street` | ✗ | Street name | `"string"` |
| `summary` | ✗ | Return aggregated summary data | `True` |
| `tax_lien` | ✗ |  | `True` |
| `vacant` | ✗ |  | `True` |
| `value` | ✗ |  | `{}` |
| `year_built` | ✗ |  | `{}` |
| `years_owned` | ✗ |  | `{}` |
| `zip` | ✗ | 5-digit ZIP code | `"string"` |

import typing

from propbolt_py.core import (
    AsyncBaseClient,
    RequestOptions,
    SyncBaseClient,
    default_request_options,
    to_encodable,
    type_utils,
)
from propbolt_py.types import models, params


class AutoCompleteClient:
    def __init__(self, *, base_client: SyncBaseClient):
        self._base_client = base_client

    def create(
        self,
        *,
        search: str,
        latitude: typing.Union[
            typing.Optional[float], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        longitude: typing.Union[
            typing.Optional[float], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        precision: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        search_types: typing.Union[
            typing.Optional[typing.List[str]], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V2AutoCompleteCreateResponse:
        """
        AutoComplete API

        The AutoComplete approximates like property searches based on incomplete address parts and combinations.
        Our AutoComplete algorithms are powered by machine learning and give you rich property lists
        without having to design tons of different Property Search queries.


        POST /v2/AutoComplete

        Args:
            latitude: float
            longitude: float
            precision: Coordinate precision digits
            search_types: Filter by search types
            search: Search term (minimum 3 characters)
            request_options: Additional options to customize the HTTP request

        Returns:
            AutoComplete suggestions retrieved successfully

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        client.v2.auto_complete.create(search="string")
        ```
        """
        _json = to_encodable(
            item={
                "latitude": latitude,
                "longitude": longitude,
                "precision": precision,
                "search_types": search_types,
                "search": search,
            },
            dump_with=params._SerializerAutoCompleteParameters,
        )
        return self._base_client.request(
            method="POST",
            path="/v2/AutoComplete",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V2AutoCompleteCreateResponse,
            request_options=request_options or default_request_options(),
        )


class AsyncAutoCompleteClient:
    def __init__(self, *, base_client: AsyncBaseClient):
        self._base_client = base_client

    async def create(
        self,
        *,
        search: str,
        latitude: typing.Union[
            typing.Optional[float], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        longitude: typing.Union[
            typing.Optional[float], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        precision: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        search_types: typing.Union[
            typing.Optional[typing.List[str]], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V2AutoCompleteCreateResponse:
        """
        AutoComplete API

        The AutoComplete approximates like property searches based on incomplete address parts and combinations.
        Our AutoComplete algorithms are powered by machine learning and give you rich property lists
        without having to design tons of different Property Search queries.


        POST /v2/AutoComplete

        Args:
            latitude: float
            longitude: float
            precision: Coordinate precision digits
            search_types: Filter by search types
            search: Search term (minimum 3 characters)
            request_options: Additional options to customize the HTTP request

        Returns:
            AutoComplete suggestions retrieved successfully

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        await client.v2.auto_complete.create(search="string")
        ```
        """
        _json = to_encodable(
            item={
                "latitude": latitude,
                "longitude": longitude,
                "precision": precision,
                "search_types": search_types,
                "search": search,
            },
            dump_with=params._SerializerAutoCompleteParameters,
        )
        return await self._base_client.request(
            method="POST",
            path="/v2/AutoComplete",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V2AutoCompleteCreateResponse,
            request_options=request_options or default_request_options(),
        )

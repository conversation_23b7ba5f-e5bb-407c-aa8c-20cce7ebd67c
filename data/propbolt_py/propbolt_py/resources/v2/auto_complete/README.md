
### AutoComplete API <a name="create"></a>

The AutoComplete approximates like property searches based on incomplete address parts and combinations. 
Our AutoComplete algorithms are powered by machine learning and give you rich property lists 
without having to design tons of different Property Search queries.


**API Endpoint**: `POST /v2/AutoComplete`

#### Synchronous Client

```python
from os import getenv
from propbolt_py import Client

client = Client(api_key=getenv("API_KEY"))
res = client.v2.auto_complete.create(search="string")

```

#### Asynchronous Client

```python
from os import getenv
from propbolt_py import AsyncClient

client = AsyncClient(api_key=getenv("API_KEY"))
res = await client.v2.auto_complete.create(search="string")

```

#### Parameters

| Parameter | Required | Description | Example |
|-----------|:--------:|-------------|--------|
| `search` | ✓ | Search term (minimum 3 characters) | `"string"` |
| `latitude` | ✗ |  | `123.0` |
| `longitude` | ✗ |  | `123.0` |
| `precision` | ✗ | Coordinate precision digits | `123` |
| `search_types` | ✗ | Filter by search types | `["string"]` |

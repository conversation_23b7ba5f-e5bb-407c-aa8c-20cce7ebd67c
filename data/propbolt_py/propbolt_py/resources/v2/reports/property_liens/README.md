
### Involuntary Liens API <a name="create"></a>

Go beyond standard tax liens and add involuntary lien data to your property insights.
Includes federal tax liens, judgment liens, mechanic's liens, and other encumbrances.


**API Endpoint**: `POST /v2/Reports/PropertyLiens`

#### Synchronous Client

```python
from os import getenv
from propbolt_py import Client

client = Client(api_key=getenv("API_KEY"))
res = client.v2.reports.property_liens.create(
    address="123 Main St, Arlington, VA 22205"
)

```

#### Asynchronous Client

```python
from os import getenv
from propbolt_py import AsyncClient

client = AsyncClient(api_key=getenv("API_KEY"))
res = await client.v2.reports.property_liens.create(
    address="123 Main St, Arlington, VA 22205"
)

```

#### Parameters

| Parameter | Required | Description | Example |
|-----------|:--------:|-------------|--------|
| `address` | ✗ | Fully formatted address | `"123 Main St, Arlington, VA 22205"` |
| `apn` | ✗ | Assessor's Parcel Number | `"string"` |
| `fips` | ✗ | FIPS county code | `"string"` |
| `id` | ✗ | Property ID from search results | `"string"` |
| `zip` | ✗ | 5-digit ZIP code | `"string"` |

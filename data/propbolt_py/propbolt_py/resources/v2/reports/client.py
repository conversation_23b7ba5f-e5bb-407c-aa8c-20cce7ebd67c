from propbolt_py.core import AsyncBaseClient, SyncBaseClient
from propbolt_py.resources.v2.reports.property_liens import (
    AsyncPropertyLiensClient,
    PropertyLiensClient,
)


class ReportsClient:
    def __init__(self, *, base_client: SyncBaseClient):
        self._base_client = base_client
        self.property_liens = PropertyLiensClient(base_client=self._base_client)


class AsyncReportsClient:
    def __init__(self, *, base_client: AsyncBaseClient):
        self._base_client = base_client
        self.property_liens = AsyncPropertyLiensClient(base_client=self._base_client)

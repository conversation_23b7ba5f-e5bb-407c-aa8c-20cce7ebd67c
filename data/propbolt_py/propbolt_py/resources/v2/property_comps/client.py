import typing

from propbolt_py.core import (
    AsyncBaseClient,
    RequestOptions,
    SyncBaseClient,
    default_request_options,
    to_encodable,
    type_utils,
)
from propbolt_py.types import models, params


class PropertyCompsClient:
    def __init__(self, *, base_client: SyncBaseClient):
        self._base_client = base_client

    def create(
        self,
        *,
        address: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        id: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V2PropertyCompsCreateResponse:
        """
        Property Comparables API v2

        Generate property comparables (comps) for valuation analysis using our standard algorithm.


        POST /v2/PropertyComps

        Args:
            address: Fully formatted address
            id: Property ID
            request_options: Additional options to customize the HTTP request

        Returns:
            Property comparables retrieved successfully

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        client.v2.property_comps.create()
        ```
        """
        _json = to_encodable(
            item={"address": address, "id": id},
            dump_with=params._SerializerPropertyCompsParameters,
        )
        return self._base_client.request(
            method="POST",
            path="/v2/PropertyComps",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V2PropertyCompsCreateResponse,
            request_options=request_options or default_request_options(),
        )


class AsyncPropertyCompsClient:
    def __init__(self, *, base_client: AsyncBaseClient):
        self._base_client = base_client

    async def create(
        self,
        *,
        address: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        id: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V2PropertyCompsCreateResponse:
        """
        Property Comparables API v2

        Generate property comparables (comps) for valuation analysis using our standard algorithm.


        POST /v2/PropertyComps

        Args:
            address: Fully formatted address
            id: Property ID
            request_options: Additional options to customize the HTTP request

        Returns:
            Property comparables retrieved successfully

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        await client.v2.property_comps.create()
        ```
        """
        _json = to_encodable(
            item={"address": address, "id": id},
            dump_with=params._SerializerPropertyCompsParameters,
        )
        return await self._base_client.request(
            method="POST",
            path="/v2/PropertyComps",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V2PropertyCompsCreateResponse,
            request_options=request_options or default_request_options(),
        )

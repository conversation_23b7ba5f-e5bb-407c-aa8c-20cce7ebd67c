
### Lender Grade AVM API <a name="create"></a>

Get the most precise property valuations using our lender-grade Automated Valuation Model 
that uses statistical modeling, recent sales data, and market-to-market analysis.


**API Endpoint**: `POST /v2/PropertyAvm`

#### Synchronous Client

```python
from os import getenv
from propbolt_py import Client

client = Client(api_key=getenv("API_KEY"))
res = client.v2.property_avm.create(address="123 Main St, Arlington, VA 22205")

```

#### Asynchronous Client

```python
from os import getenv
from propbolt_py import AsyncClient

client = AsyncClient(api_key=getenv("API_KEY"))
res = await client.v2.property_avm.create(address="123 Main St, Arlington, VA 22205")

```

#### Parameters

| Parameter | Required | Description | Example |
|-----------|:--------:|-------------|--------|
| `address` | ✗ | Fully formatted address | `"123 Main St, Arlington, VA 22205"` |
| `id` | ✗ | Property ID from search results | `"string"` |
| `strict` | ✗ | Enable strict address matching | `True` |

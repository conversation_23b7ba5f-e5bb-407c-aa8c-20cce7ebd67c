
### Address Verification API <a name="create"></a>

Verify 1 - 100 addresses at a time for accuracy and standardization.


**API Endpoint**: `POST /v2/AddressVerification`

#### Synchronous Client

```python
from os import getenv
from propbolt_py import Client

client = Client(api_key=getenv("API_KEY"))
res = client.v2.address_verification.create(addresses=[{}])

```

#### Asynchronous Client

```python
from os import getenv
from propbolt_py import AsyncClient

client = AsyncClient(api_key=getenv("API_KEY"))
res = await client.v2.address_verification.create(addresses=[{}])

```

#### Parameters

| Parameter | Required | Description | Example |
|-----------|:--------:|-------------|--------|
| `addresses` | ✓ |  | `[{}]` |
| `strict` | ✗ | Enable strict verification mode | `True` |

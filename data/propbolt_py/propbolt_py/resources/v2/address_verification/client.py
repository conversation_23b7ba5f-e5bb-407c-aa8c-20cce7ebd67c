import typing

from propbolt_py.core import (
    AsyncBaseClient,
    RequestOptions,
    SyncBaseClient,
    default_request_options,
    to_encodable,
    type_utils,
)
from propbolt_py.types import models, params


class AddressVerificationClient:
    def __init__(self, *, base_client: SyncBaseClient):
        self._base_client = base_client

    def create(
        self,
        *,
        addresses: typing.List[params.AddressToVerify],
        strict: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V2AddressVerificationCreateResponse:
        """
        Address Verification API

        Verify 1 - 100 addresses at a time for accuracy and standardization.


        POST /v2/AddressVerification

        Args:
            strict: Enable strict verification mode
            addresses: typing.List[AddressToVerify]
            request_options: Additional options to customize the HTTP request

        Returns:
            Address verification results

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        client.v2.address_verification.create(addresses=[{}])
        ```
        """
        _json = to_encodable(
            item={"strict": strict, "addresses": addresses},
            dump_with=params._SerializerAddressVerificationParameters,
        )
        return self._base_client.request(
            method="POST",
            path="/v2/AddressVerification",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V2AddressVerificationCreateResponse,
            request_options=request_options or default_request_options(),
        )


class AsyncAddressVerificationClient:
    def __init__(self, *, base_client: AsyncBaseClient):
        self._base_client = base_client

    async def create(
        self,
        *,
        addresses: typing.List[params.AddressToVerify],
        strict: typing.Union[
            typing.Optional[bool], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V2AddressVerificationCreateResponse:
        """
        Address Verification API

        Verify 1 - 100 addresses at a time for accuracy and standardization.


        POST /v2/AddressVerification

        Args:
            strict: Enable strict verification mode
            addresses: typing.List[AddressToVerify]
            request_options: Additional options to customize the HTTP request

        Returns:
            Address verification results

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        await client.v2.address_verification.create(addresses=[{}])
        ```
        """
        _json = to_encodable(
            item={"strict": strict, "addresses": addresses},
            dump_with=params._SerializerAddressVerificationParameters,
        )
        return await self._base_client.request(
            method="POST",
            path="/v2/AddressVerification",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V2AddressVerificationCreateResponse,
            request_options=request_options or default_request_options(),
        )

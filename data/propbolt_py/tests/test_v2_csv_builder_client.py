import pydantic
import pytest

from propbolt_py import AsyncClient, Client
from propbolt_py.environment import Environment
from propbolt_py.types import models


def test_create_200_success_default():
    """Tests a POST request to the /v2/CSVBuilder endpoint.

    Operation: create
    Test Case ID: success_default
    Expected Status: 200
    Mode: Synchronous execution

    Response : models.V2CsvBuilderCreateResponse

    Validates:
    - Authentication requirements are satisfied
    - All required input parameters are properly handled
    - Response status code is correct
    - Response data matches expected schema

    This test uses example data to verify the endpoint behavior.
    """
    # tests calling sync method with example data
    client = Client(api_key="API_KEY", environment=Environment.MOCK_SERVER)
    response = client.v2.csv_builder.create(
        file_name="property_export_2024",
        map=["id", "address", "estimatedValue", "bedrooms", "bathrooms"],
    )
    try:
        pydantic.TypeAdapter(models.V2CsvBuilderCreateResponse).validate_python(
            response
        )
        is_valid_response_schema = True
    except pydantic.ValidationError:
        is_valid_response_schema = False
    assert is_valid_response_schema, "failed response type check"


@pytest.mark.asyncio
async def test_await_create_200_success_default():
    """Tests a POST request to the /v2/CSVBuilder endpoint.

    Operation: create
    Test Case ID: success_default
    Expected Status: 200
    Mode: Asynchronous execution

    Response : models.V2CsvBuilderCreateResponse

    Validates:
    - Authentication requirements are satisfied
    - All required input parameters are properly handled
    - Response status code is correct
    - Response data matches expected schema

    This test uses example data to verify the endpoint behavior.
    """
    # tests calling async method with example data
    client = AsyncClient(api_key="API_KEY", environment=Environment.MOCK_SERVER)
    response = await client.v2.csv_builder.create(
        file_name="property_export_2024",
        map=["id", "address", "estimatedValue", "bedrooms", "bathrooms"],
    )
    try:
        pydantic.TypeAdapter(models.V2CsvBuilderCreateResponse).validate_python(
            response
        )
        is_valid_response_schema = True
    except pydantic.ValidationError:
        is_valid_response_schema = False
    assert is_valid_response_schema, "failed response type check"

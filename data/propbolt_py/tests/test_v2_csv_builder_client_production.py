import os
import pydantic
import pytest

from propbolt_py import AsyncClient, Client
from propbolt_py.environment import Environment
from propbolt_py.types import models


def test_create_200_success_production():
    """Tests a POST request to the /v2/CSVBuilder endpoint in production.

    Operation: create
    Test Case ID: success_production
    Expected Status: 200
    Mode: Synchronous execution

    Response : models.V2CsvBuilderCreateResponse

    Validates:
    - Authentication requirements are satisfied
    - All required input parameters are properly handled
    - Response status code is correct
    - Response data matches expected schema

    This test uses production environment with real API key.
    """
    # Skip test if no API key is provided
    api_key = os.getenv("API_KEY")
    if not api_key:
        pytest.skip("API_KEY environment variable not set")
    
    # tests calling sync method with production environment
    client = Client(api_key=api_key, environment=Environment.ENVIRONMENT)
    response = client.v2.csv_builder.create(
        file_name="property_export_2024",
        map=["propertyId", "address", "estimatedValue", "bedrooms", "bathrooms"],
    )
    try:
        pydantic.TypeAdapter(models.V2CsvBuilderCreateResponse).validate_python(
            response
        )
        is_valid_response_schema = True
    except pydantic.ValidationError:
        is_valid_response_schema = False
    assert is_valid_response_schema, "failed response type check"


@pytest.mark.asyncio
async def test_await_create_200_success_production():
    """Tests a POST request to the /v2/CSVBuilder endpoint in production.

    Operation: create
    Test Case ID: success_production
    Expected Status: 200
    Mode: Asynchronous execution

    Response : models.V2CsvBuilderCreateResponse

    Validates:
    - Authentication requirements are satisfied
    - All required input parameters are properly handled
    - Response status code is correct
    - Response data matches expected schema

    This test uses production environment with real API key.
    """
    # Skip test if no API key is provided
    api_key = os.getenv("API_KEY")
    if not api_key:
        pytest.skip("API_KEY environment variable not set")
    
    # tests calling async method with production environment
    client = AsyncClient(api_key=api_key, environment=Environment.ENVIRONMENT)
    response = await client.v2.csv_builder.create(
        file_name="property_export_2024",
        map=["propertyId", "address", "estimatedValue", "bedrooms", "bathrooms"],
    )
    try:
        pydantic.TypeAdapter(models.V2CsvBuilderCreateResponse).validate_python(
            response
        )
        is_valid_response_schema = True
    except pydantic.ValidationError:
        is_valid_response_schema = False
    assert is_valid_response_schema, "failed response type check"


def test_create_200_success_default_production():
    """Tests that the default client configuration uses production environment.

    This test verifies that when no environment is specified, the client
    defaults to production environment.
    """
    # Skip test if no API key is provided
    api_key = os.getenv("API_KEY")
    if not api_key:
        pytest.skip("API_KEY environment variable not set")
    
    # tests calling sync method with default environment (should be production)
    client = Client(api_key=api_key)  # No environment specified - should default to production
    response = client.v2.csv_builder.create(
        file_name="property_export_2024",
        map=["propertyId", "address", "estimatedValue", "bedrooms", "bathrooms"],
    )
    try:
        pydantic.TypeAdapter(models.V2CsvBuilderCreateResponse).validate_python(
            response
        )
        is_valid_response_schema = True
    except pydantic.ValidationError:
        is_valid_response_schema = False
    assert is_valid_response_schema, "failed response type check"

#!/bin/bash

# PropBolt Data API - Deployment Script with API Key Management
# Deploy the enhanced data API with PostgreSQL-based API key management

echo "🚀 PropBolt Data API - Enhanced Deployment"
echo "=========================================="

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI is not installed. Please install Google Cloud SDK first."
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ Not authenticated with Google Cloud. Please run 'gcloud auth login' first."
    exit 1
fi

# Set project
echo "🔧 Setting Google Cloud project..."
gcloud config set project gold-braid-458901-v2

# Enable required APIs
echo "🔌 Enabling required APIs..."
gcloud services enable appengine.googleapis.com
gcloud services enable sqladmin.googleapis.com
gcloud services enable cloudbuild.googleapis.com

# Check if database initialization is needed
echo "🗄️  Checking database setup..."
read -p "Do you want to initialize the API key management database tables? (y/N): " init_db

if [[ $init_db =~ ^[Yy]$ ]]; then
    echo "📋 Initializing API key management database..."
    
    # Set environment variables for database initialization
    export DB_HOST=*************
    export DB_PORT=5432
    export DB_NAME=propbolt
    export DB_USER=propbolt_user
    export DB_PASSWORD=PropBolt2024!
    export DB_SSL_MODE=require
    
    # Run database initialization
    python3 init_api_keys_db.py
    
    if [ $? -ne 0 ]; then
        echo "❌ Database initialization failed"
        exit 1
    fi
    
    echo "✅ Database initialized successfully"
fi

# Deploy Data API Server
echo "📊 Deploying Data API Server (data.propbolt.com)..."
gcloud app deploy app.yaml --service=data --quiet --promote

if [ $? -ne 0 ]; then
    echo "❌ Data API deployment failed"
    exit 1
fi

echo "✅ Data API deployed successfully"

# Update dispatch configuration (if needed)
echo "🔀 Checking dispatch configuration..."
if [ -f "../dispatch.yaml" ]; then
    echo "📋 Updating dispatch configuration..."
    cd ..
    gcloud app deploy dispatch.yaml --quiet
    if [ $? -ne 0 ]; then
        echo "❌ Dispatch deployment failed"
        exit 1
    fi
    cd data
    echo "✅ Dispatch configuration updated"
else
    echo "⚠️  No dispatch.yaml found in parent directory"
fi

# Get deployment URLs
echo ""
echo "🎉 Deployment Complete!"
echo "======================="
echo "📊 Data API: https://data-dot-gold-braid-458901-v2.uc.r.appspot.com"
echo ""
echo "🌐 Custom Domain:"
echo "📊 data.propbolt.com → Data API"
echo ""
echo "🔧 API Key Management Features:"
echo "✅ PostgreSQL-based API key storage"
echo "✅ Rate limiting and quota enforcement"
echo "✅ Usage analytics and logging"
echo "✅ Admin endpoints for key management"
echo "✅ Secure API key validation"
echo ""
echo "📋 Next Steps:"
echo "1. Test the health endpoint: curl https://data.propbolt.com/health"
echo "2. Generate API keys using admin endpoints"
echo "3. Test protected endpoints with valid API keys"
echo "4. Monitor usage through analytics endpoints"
echo ""
echo "🔑 Admin Endpoints:"
echo "POST /admin/keys/generate - Generate new API keys"
echo "GET /admin/keys - List all API keys"
echo "DELETE /admin/keys/{key_id} - Revoke API keys"
echo "GET /admin/usage/{key_id} - Get usage analytics"
echo ""
echo "🔐 Protected Endpoints (require API key):"
echo "POST /v2/PropertySearch"
echo "POST /v2/PropertyDetail"
echo "POST /v2/PropertyDetailBulk"
echo "POST /v1/PropertyParcel"
echo "POST /v2/PropertyComps"
echo "POST /v3/PropertyComps"
echo "POST /v2/AutoComplete"
echo "POST /v2/AddressVerification"
echo "POST /v2/PropGPT"
echo "POST /v2/CSVBuilder"
echo "POST /v2/PropertyAvm"
echo "POST /v2/Reports/PropertyLiens"
echo "POST /v2/PropertyMapping"
echo ""
echo "📖 Documentation:"
echo "GET /docs - Interactive API documentation"
echo ""
echo "⚠️  Important Security Notes:"
echo "1. Change the admin token in production (currently: admin_token_placeholder)"
echo "2. Integrate with BetterAuth for proper admin authentication"
echo "3. Use HTTPS for all API key transmissions"
echo "4. Monitor API usage and set appropriate rate limits"
echo ""
echo "🧪 Testing:"
echo "Run 'python3 test_api_keys.py' to test the API key management system"

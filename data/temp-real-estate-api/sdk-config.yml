api: real-estate-api
languages:
  java:
    sdk_name: realestateapijava
    group_id: com.yourorg
  python:
    sdk_name: real_estate_api_py
  typescript:
    sdk_name: real_estate_api_ts
  rust:
    sdk_name: real_estate_api_rs
    cli: false
  go:
    sdk_name: real_estate_api_go
servers:
- url: https://api.realestateapi.com
  name: production
  default: true
auths:
- id: ApiKeyAuth
  param: api_key
default_module_structure: path
modules:
- path: property
  operations:
  - id: property-parcel-api
    function_name: parcel
  - id: property-comps-api
    function_name: comparables
  - id: property-detail-api
    function_name: details
  - id: property-detail-bulk-api
    function_name: bulk_details
  - id: beta-mapping-pins-api
    function_name: pins
  - id: property-search-api
    function_name: search
  - id: v3-property-comps-api
    function_name: comparables_advanced
- path: address
  operations:
  - id: address-verification-api
    function_name: verify
  - id: autocomplete-api
    function_name: auto_complete
- path: reporting
  operations:
  - id: csv-generator-api
    function_name: generate_csv
  - id: involuntary-lien-api
    function_name: lien
- path: gpt
  operations:
  - id: propgpt-api
    function_name: chat
- path: avm
  operations:
  - id: lender-grade-avm-api
    function_name: get_valuation


#!/usr/bin/env node

/**
 * PropBolt Brain - Admin User Creation Script
 * 
 * This script creates an admin user for the PropBolt Brain platform.
 * Run this script to create your first admin user.
 * 
 * Usage: node scripts/create-admin.js
 */

const readline = require('readline');
const crypto = require('crypto');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

function questionHidden(prompt) {
  return new Promise((resolve) => {
    process.stdout.write(prompt);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    let password = '';
    process.stdin.on('data', function(char) {
      char = char + '';
      
      switch(char) {
        case '\n':
        case '\r':
        case '\u0004':
          process.stdin.setRawMode(false);
          process.stdin.pause();
          process.stdout.write('\n');
          resolve(password);
          break;
        case '\u0003':
          process.exit();
          break;
        case '\u007f': // backspace
          if (password.length > 0) {
            password = password.slice(0, -1);
            process.stdout.write('\b \b');
          }
          break;
        default:
          password += char;
          process.stdout.write('*');
          break;
      }
    });
  });
}

async function createAdminUser() {
  console.log('\n🚀 PropBolt Brain - Admin User Creation\n');
  console.log('This script will create an admin user for your PropBolt Brain platform.\n');

  try {
    const name = await question('Enter admin name: ');
    const email = await question('Enter admin email: ');
    const password = await questionHidden('Enter admin password: ');
    const confirmPassword = await questionHidden('Confirm admin password: ');

    if (password !== confirmPassword) {
      console.log('\n❌ Passwords do not match. Please try again.');
      rl.close();
      return;
    }

    if (password.length < 8) {
      console.log('\n❌ Password must be at least 8 characters long.');
      rl.close();
      return;
    }

    // Generate admin credentials
    const adminUser = {
      id: crypto.randomUUID(),
      name: name.trim(),
      email: email.trim().toLowerCase(),
      password: password,
      role: 'admin',
      createdAt: new Date().toISOString(),
    };

    console.log('\n✅ Admin user details:');
    console.log(`   Name: ${adminUser.name}`);
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Role: ${adminUser.role}`);
    console.log(`   ID: ${adminUser.id}`);

    console.log('\n📝 To use this admin account:');
    console.log('   1. Start your PropBolt Brain frontend server');
    console.log('   2. Go to http://localhost:3000/login');
    console.log(`   3. Sign in with email: ${adminUser.email}`);
    console.log('   4. Use the password you just created');

    console.log('\n🔐 For production deployment:');
    console.log('   1. Set up your PostgreSQL database');
    console.log('   2. Configure DATABASE_URL in your environment');
    console.log('   3. Run database migrations');
    console.log('   4. Create admin users through the proper API endpoints');

    // Save to a temporary file for reference
    const fs = require('fs');
    const adminData = {
      ...adminUser,
      password: '[REDACTED]', // Don't save the actual password
      note: 'This is a development admin user. For production, use proper database setup.'
    };
    
    fs.writeFileSync('admin-user.json', JSON.stringify(adminData, null, 2));
    console.log('\n💾 Admin user info saved to admin-user.json (password not included)');

  } catch (error) {
    console.error('\n❌ Error creating admin user:', error.message);
  } finally {
    rl.close();
  }
}

// Run the script
createAdminUser();

#!/bin/bash

# Script to download all files from the latest API1 deployment
# Based on the deployment information from gcloud app versions describe

echo "Downloading API1 deployment files..."

# Create directory structure
mkdir -p downloaded-api1-source/database
mkdir -p downloaded-api1-source/real_estate_api_py/core
mkdir -p downloaded-api1-source/real_estate_api_py/resources/address
mkdir -p downloaded-api1-source/real_estate_api_py/resources/avm
mkdir -p downloaded-api1-source/real_estate_api_py/resources/gpt
mkdir -p downloaded-api1-source/real_estate_api_py/resources/property
mkdir -p downloaded-api1-source/real_estate_api_py/resources/reporting
mkdir -p downloaded-api1-source/real_estate_api_py/types/models
mkdir -p downloaded-api1-source/real_estate_api_py/types/params
mkdir -p downloaded-api1-source/tests

# Base URL for Google Cloud Storage
BASE_URL="gs://staging.gold-braid-458901-v2.appspot.com"

# Download main files
echo "Downloading main files..."
gsutil cp ${BASE_URL}/86b6716ebb0de668d44a161b37c08c9f7998a2c4 downloaded-api1-source/main.py
gsutil cp ${BASE_URL}/9d3c83f95919c37ab9d4fec37badf539390461e9 downloaded-api1-source/app.yaml
gsutil cp ${BASE_URL}/2baaddae3cdae080b8de981df40a1f4611bd7da5 downloaded-api1-source/requirements.txt
gsutil cp ${BASE_URL}/56b14ce663da2650e38dc1a9dbc032e341a37fa5 downloaded-api1-source/main_complex.py
gsutil cp ${BASE_URL}/33355cc5366c06565b7f2e7cb9eb3708293efc77 downloaded-api1-source/main_simple.py
gsutil cp ${BASE_URL}/9a72c04322f87d8f79cb96dd1dbd076dd71a4548 downloaded-api1-source/main_test.py

# Download documentation and config files
echo "Downloading documentation and config files..."
gsutil cp ${BASE_URL}/d93929dacb58d1f78dc274b3f9577b3987fde731 downloaded-api1-source/API_KEY_MANAGEMENT.md
gsutil cp ${BASE_URL}/030cb338df9bdde9bc43f8e5969bea1a562f397d downloaded-api1-source/README.md
gsutil cp ${BASE_URL}/eec792247d6ab492369a544be7142aace0c3e59f downloaded-api1-source/pyproject.toml
gsutil cp ${BASE_URL}/3a0e0e1c65d0bd186edb79e657633b9ecc80e30e downloaded-api1-source/poetry.lock
gsutil cp ${BASE_URL}/691485c5bc030b9391a352644689cfe2b7bb7b75 downloaded-api1-source/sdk-config.yml

# Download OpenAPI specs
echo "Downloading OpenAPI specifications..."
gsutil cp ${BASE_URL}/e16879abbd2e89d2d0bb93b915ccd7774266a6a3 downloaded-api1-source/adjusted-openapi.json
gsutil cp ${BASE_URL}/7f6ad873e44dfda47cba7cf5e20702a7bc59c193 downloaded-api1-source/new-openapi.yaml
gsutil cp ${BASE_URL}/7f6ad873e44dfda47cba7cf5e20702a7bc59c193 downloaded-api1-source/openapi-complete.yaml
gsutil cp ${BASE_URL}/0d96a918a6d77e8837b1bd26a765175b7c2f669e downloaded-api1-source/openapi-endpoints.yaml
gsutil cp ${BASE_URL}/f5c97404b051f59071869618deb13132f3c686ea downloaded-api1-source/openapi-original.json

# Download database files
echo "Downloading database files..."
gsutil cp ${BASE_URL}/21b3c33345b319faa5ed7e556ff3e2287e70cafb downloaded-api1-source/database/connection.py
gsutil cp ${BASE_URL}/3a9b1cc6eac12dee301c9f5426e5e2089a25985c downloaded-api1-source/database/middleware.py
gsutil cp ${BASE_URL}/25d98111677c2ec9dcdf68103c5218a5b0a09b7c downloaded-api1-source/database/migrations.py
gsutil cp ${BASE_URL}/f600bb91b5a0b0dc813e25ec3e6f2c079c84dcfc downloaded-api1-source/database/models.py
gsutil cp ${BASE_URL}/d470b16cc9edbeb320c7822b4c9192658354d7f8 downloaded-api1-source/database/services.py

echo "Download complete! Files saved to downloaded-api1-source/"
echo "You can now compare with your local api1/ directory"

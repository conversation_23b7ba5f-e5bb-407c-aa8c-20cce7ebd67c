#!/bin/bash

# =============================================================================
# PropBolt API2 DNS Configuration Script
# Sets up DNS records for api2.propbolt.com using Google Cloud DNS
# =============================================================================

set -e  # Exit on any error

echo "🌐 PropBolt API2 DNS Configuration"
echo "=================================="

# Configuration
PROJECT_ID="gold-braid-458901-v2"
ZONE_NAME="propbolt-com"
DOMAIN="api2.propbolt.com"
REGION="us-central1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if gcloud is installed and authenticated
check_gcloud() {
    log_info "Checking gcloud configuration..."
    
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "Not authenticated with gcloud. Please run 'gcloud auth login'"
        exit 1
    fi
    
    # Set project
    gcloud config set project $PROJECT_ID
    log_success "gcloud configured for project: $PROJECT_ID"
}

# Check if DNS zone exists
check_dns_zone() {
    log_info "Checking DNS zone configuration..."
    
    if gcloud dns managed-zones describe $ZONE_NAME --format="value(name)" &>/dev/null; then
        log_success "DNS zone '$ZONE_NAME' exists"
    else
        log_error "DNS zone '$ZONE_NAME' not found. Please create it first:"
        echo "  gcloud dns managed-zones create $ZONE_NAME --dns-name=propbolt.com. --description='PropBolt DNS Zone'"
        exit 1
    fi
}

# Get App Engine service URL
get_app_engine_url() {
    log_info "Getting App Engine service URL..."
    
    # Get the App Engine service URL
    SERVICE_URL=$(gcloud app browse --service=api2 --no-launch-browser 2>/dev/null | grep -o 'https://[^[:space:]]*' || echo "")
    
    if [ -z "$SERVICE_URL" ]; then
        log_error "Could not determine App Engine service URL. Make sure the api2 service is deployed."
        exit 1
    fi
    
    log_success "App Engine service URL: $SERVICE_URL"
    
    # Extract the hostname for CNAME record
    APP_ENGINE_HOST=$(echo $SERVICE_URL | sed 's|https://||' | sed 's|/.*||')
    log_info "App Engine hostname: $APP_ENGINE_HOST"
}

# Create DNS records
create_dns_records() {
    log_info "Creating DNS records for $DOMAIN..."
    
    # Start a transaction
    gcloud dns record-sets transaction start --zone=$ZONE_NAME
    
    # Check if A record already exists
    if gcloud dns record-sets list --zone=$ZONE_NAME --name="$DOMAIN." --type=A --format="value(name)" | grep -q "$DOMAIN."; then
        log_warning "A record for $DOMAIN already exists, removing it first"
        EXISTING_A_RECORD=$(gcloud dns record-sets list --zone=$ZONE_NAME --name="$DOMAIN." --type=A --format="value(rrdatas[0])")
        gcloud dns record-sets transaction remove --zone=$ZONE_NAME --name="$DOMAIN." --type=A --rdata="$EXISTING_A_RECORD"
    fi
    
    # Check if CNAME record already exists
    if gcloud dns record-sets list --zone=$ZONE_NAME --name="$DOMAIN." --type=CNAME --format="value(name)" | grep -q "$DOMAIN."; then
        log_warning "CNAME record for $DOMAIN already exists, removing it first"
        EXISTING_CNAME_RECORD=$(gcloud dns record-sets list --zone=$ZONE_NAME --name="$DOMAIN." --type=CNAME --format="value(rrdatas[0])")
        gcloud dns record-sets transaction remove --zone=$ZONE_NAME --name="$DOMAIN." --type=CNAME --rdata="$EXISTING_CNAME_RECORD"
    fi
    
    # Add CNAME record pointing to App Engine
    gcloud dns record-sets transaction add --zone=$ZONE_NAME --name="$DOMAIN." --type=CNAME --rdata="$APP_ENGINE_HOST." --ttl=300
    
    # Execute the transaction
    gcloud dns record-sets transaction execute --zone=$ZONE_NAME
    
    log_success "DNS records created successfully"
}

# Verify DNS configuration
verify_dns() {
    log_info "Verifying DNS configuration..."
    
    # List the DNS records
    log_info "Current DNS records for $DOMAIN:"
    gcloud dns record-sets list --zone=$ZONE_NAME --name="$DOMAIN." --format="table(name,type,rrdatas,ttl)"
    
    log_info "DNS propagation may take a few minutes to complete."
    log_info "You can check DNS propagation using: dig $DOMAIN"
}

# Test the domain
test_domain() {
    log_info "Testing domain accessibility..."
    
    # Wait a moment for DNS to propagate
    sleep 5
    
    # Test if the domain resolves
    if nslookup $DOMAIN > /dev/null 2>&1; then
        log_success "Domain $DOMAIN resolves successfully"
        
        # Test HTTP connectivity
        if curl -s -f "https://$DOMAIN/health" > /dev/null; then
            log_success "HTTPS connectivity test passed"
        else
            log_warning "HTTPS connectivity test failed - this is normal if the service isn't deployed yet"
        fi
    else
        log_warning "Domain resolution test failed - DNS may still be propagating"
    fi
}

# Main function
main() {
    log_info "Starting DNS configuration for $DOMAIN..."
    
    # Check prerequisites
    check_gcloud
    check_dns_zone
    get_app_engine_url
    
    # Configure DNS
    create_dns_records
    verify_dns
    test_domain
    
    log_success "DNS configuration completed!"
    echo ""
    echo "🌐 DNS Configuration Summary:"
    echo "   • Domain: $DOMAIN"
    echo "   • Points to: $APP_ENGINE_HOST"
    echo "   • Zone: $ZONE_NAME"
    echo ""
    echo "📋 Next Steps:"
    echo "   1. Wait 5-10 minutes for DNS propagation"
    echo "   2. Test the domain: curl https://$DOMAIN/health"
    echo "   3. Configure SSL certificate if needed"
    echo ""
    echo "🔍 Monitoring:"
    echo "   • DNS Console: https://console.cloud.google.com/net-services/dns/zones/$ZONE_NAME/rrsets/list?project=$PROJECT_ID"
    echo "   • Domain Mapping: https://console.cloud.google.com/appengine/settings/domains?project=$PROJECT_ID"
    echo ""
    log_info "DNS setup complete! 🎉"
}

# Run main function
main "$@"

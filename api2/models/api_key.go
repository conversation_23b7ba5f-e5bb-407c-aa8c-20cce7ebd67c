package models

import (
	"crypto/sha256"
	"database/sql"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// APIKey represents an API key in the database
type APIKey struct {
	ID                 int       `json:"id" db:"id"`
	KeyHash            string    `json:"-" db:"key_hash"`
	KeyPrefix          string    `json:"key_prefix" db:"key_prefix"`
	UserID             string    `json:"user_id" db:"user_id"`
	Name               string    `json:"name" db:"name"`
	Tier               string    `json:"tier" db:"tier"`
	IsActive           bool      `json:"is_active" db:"is_active"`
	CreatedAt          time.Time `json:"created_at" db:"created_at"`
	ExpiresAt          *time.Time `json:"expires_at" db:"expires_at"`
	RevokedAt          *time.Time `json:"revoked_at" db:"revoked_at"`
	LastUsedAt         *time.Time `json:"last_used_at" db:"last_used_at"`
	RequestsPerMinute  int       `json:"requests_per_minute" db:"requests_per_minute"`
	RequestsPerDay     int       `json:"requests_per_day" db:"requests_per_day"`
	AllowedEndpoints   []string  `json:"allowed_endpoints" db:"allowed_endpoints"`
	Metadata           string    `json:"metadata" db:"metadata"`
}

// APIUsage represents a usage log entry
type APIUsage struct {
	ID             int64     `json:"id" db:"id"`
	KeyID          int       `json:"key_id" db:"key_id"`
	Endpoint       string    `json:"endpoint" db:"endpoint"`
	Method         string    `json:"method" db:"method"`
	Timestamp      time.Time `json:"timestamp" db:"timestamp"`
	ResponseStatus int       `json:"response_status" db:"response_status"`
	ResponseTimeMs *int      `json:"response_time_ms" db:"response_time_ms"`
	IPAddress      *string   `json:"ip_address" db:"ip_address"`
	UserAgent      *string   `json:"user_agent" db:"user_agent"`
	RequestSize    *int      `json:"request_size" db:"request_size"`
	ResponseSize   *int      `json:"response_size" db:"response_size"`
	ErrorMessage   *string   `json:"error_message" db:"error_message"`
}

// DailyUsageSummary represents daily usage statistics
type DailyUsageSummary struct {
	ID                   int64     `json:"id" db:"id"`
	KeyID                int       `json:"key_id" db:"key_id"`
	Date                 time.Time `json:"date" db:"date"`
	TotalRequests        int       `json:"total_requests" db:"total_requests"`
	SuccessfulRequests   int       `json:"successful_requests" db:"successful_requests"`
	FailedRequests       int       `json:"failed_requests" db:"failed_requests"`
	TotalResponseTimeMs  int64     `json:"total_response_time_ms" db:"total_response_time_ms"`
	AvgResponseTimeMs    *float64  `json:"avg_response_time_ms" db:"avg_response_time_ms"`
	EndpointsUsed        []string  `json:"endpoints_used" db:"endpoints_used"`
	CreatedAt            time.Time `json:"created_at" db:"created_at"`
	UpdatedAt            time.Time `json:"updated_at" db:"updated_at"`
}

// RateLimitWindow represents a rate limiting window
type RateLimitWindow struct {
	ID           int64     `json:"id" db:"id"`
	KeyID        int       `json:"key_id" db:"key_id"`
	WindowStart  time.Time `json:"window_start" db:"window_start"`
	WindowType   string    `json:"window_type" db:"window_type"`
	RequestCount int       `json:"request_count" db:"request_count"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// GenerateAPIKey generates a new API key with the specified prefix
func GenerateAPIKey(keyType string) string {
	var prefix string
	switch keyType {
	case "live":
		prefix = "pb_live_"
	case "test":
		prefix = "pb_test_"
	default:
		prefix = "pb_live_"
	}
	
	// Generate UUID and remove hyphens
	id := uuid.New().String()
	id = fmt.Sprintf("%s%s", prefix, id)
	return id
}

// HashAPIKey creates a SHA-256 hash of the API key
func HashAPIKey(apiKey string) string {
	hash := sha256.Sum256([]byte(apiKey))
	return fmt.Sprintf("%x", hash)
}

// ValidateAPIKey checks if an API key exists and is valid
func ValidateAPIKey(db *sql.DB, apiKey string) (*APIKey, error) {
	keyHash := HashAPIKey(apiKey)
	
	query := `
		SELECT id, key_hash, key_prefix, user_id, name, tier, is_active, 
		       created_at, expires_at, revoked_at, last_used_at, 
		       requests_per_minute, requests_per_day, allowed_endpoints, metadata
		FROM api2_keys 
		WHERE key_hash = $1 AND is_active = true AND revoked_at IS NULL
		AND (expires_at IS NULL OR expires_at > NOW())
	`
	
	var key APIKey
	var allowedEndpoints sql.NullString
	var metadata sql.NullString
	
	err := db.QueryRow(query, keyHash).Scan(
		&key.ID, &key.KeyHash, &key.KeyPrefix, &key.UserID, &key.Name,
		&key.Tier, &key.IsActive, &key.CreatedAt, &key.ExpiresAt,
		&key.RevokedAt, &key.LastUsedAt, &key.RequestsPerMinute,
		&key.RequestsPerDay, &allowedEndpoints, &metadata,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("invalid API key")
		}
		return nil, fmt.Errorf("database error: %v", err)
	}
	
	// Handle nullable fields
	if allowedEndpoints.Valid {
		// Parse JSON array if needed
		key.Metadata = allowedEndpoints.String
	}
	if metadata.Valid {
		key.Metadata = metadata.String
	}
	
	return &key, nil
}

// UpdateLastUsed updates the last_used_at timestamp for an API key
func (k *APIKey) UpdateLastUsed(db *sql.DB) error {
	query := `UPDATE api2_keys SET last_used_at = NOW() WHERE id = $1`
	_, err := db.Exec(query, k.ID)
	return err
}

// LogUsage logs API usage to the database
func LogUsage(db *sql.DB, usage *APIUsage) error {
	query := `
		INSERT INTO api2_usage (key_id, endpoint, method, response_status, 
		                       response_time_ms, ip_address, user_agent, 
		                       request_size, response_size, error_message)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
	`
	
	_, err := db.Exec(query, usage.KeyID, usage.Endpoint, usage.Method,
		usage.ResponseStatus, usage.ResponseTimeMs, usage.IPAddress,
		usage.UserAgent, usage.RequestSize, usage.ResponseSize, usage.ErrorMessage)
	
	return err
}

// CheckRateLimit checks if the API key has exceeded its rate limit
func CheckRateLimit(db *sql.DB, keyID int, windowType string, limit int) (bool, error) {
	var windowStart time.Time
	now := time.Now()
	
	switch windowType {
	case "minute":
		windowStart = now.Truncate(time.Minute)
	case "hour":
		windowStart = now.Truncate(time.Hour)
	case "day":
		windowStart = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	default:
		return false, fmt.Errorf("invalid window type: %s", windowType)
	}
	
	// Get current request count for this window
	query := `
		SELECT COALESCE(request_count, 0) 
		FROM api2_rate_limit_windows 
		WHERE key_id = $1 AND window_start = $2 AND window_type = $3
	`
	
	var currentCount int
	err := db.QueryRow(query, keyID, windowStart, windowType).Scan(&currentCount)
	if err != nil && err != sql.ErrNoRows {
		return false, fmt.Errorf("database error: %v", err)
	}
	
	// Check if limit exceeded
	if currentCount >= limit {
		return true, nil
	}
	
	// Increment counter
	upsertQuery := `
		INSERT INTO api2_rate_limit_windows (key_id, window_start, window_type, request_count)
		VALUES ($1, $2, $3, 1)
		ON CONFLICT (key_id, window_start, window_type)
		DO UPDATE SET request_count = api2_rate_limit_windows.request_count + 1, updated_at = NOW()
	`
	
	_, err = db.Exec(upsertQuery, keyID, windowStart, windowType)
	if err != nil {
		return false, fmt.Errorf("failed to update rate limit: %v", err)
	}
	
	return false, nil
}

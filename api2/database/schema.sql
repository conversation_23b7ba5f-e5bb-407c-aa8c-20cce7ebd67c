-- PropBolt API2 Key Management System - Database Schema
-- Compatible with existing Python API system and new Go API2 system

-- Create API2 Keys table (separate from existing api_keys for api2 service)
CREATE TABLE IF NOT EXISTS "api2_keys" (
    "id" SERIAL PRIMARY KEY,
    "key_hash" TEXT UNIQUE NOT NULL, -- SHA-256 hash of the API key
    "key_prefix" TEXT NOT NULL, -- First 8 characters for identification (pb_live_, pb_test_)
    "user_id" TEXT REFERENCES "user"("id") ON DELETE CASCADE,
    "name" TEXT NOT NULL, -- Human-readable name for the key
    "tier" TEXT NOT NULL DEFAULT 'basic', -- basic, premium, enterprise
    "is_active" BOOLEAN DEFAULT TRUE,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "expires_at" TIMESTAMP, -- NULL for no expiration
    "revoked_at" TIMESTAMP, -- When the key was revoked
    "last_used_at" TIMESTAMP, -- Last time the key was used
    "requests_per_minute" INTEGER DEFAULT 60, -- Rate limit per minute
    "requests_per_day" INTEGER DEFAULT 1000, -- Daily quota
    "allowed_endpoints" TEXT[], -- Array of allowed endpoints, NULL for all
    "metadata" JSONB DEFAULT '{}' -- Additional metadata
);

-- API2 Usage table - Log all API2 requests for analytics and billing
CREATE TABLE IF NOT EXISTS "api2_usage" (
    "id" BIGSERIAL PRIMARY KEY,
    "key_id" INTEGER NOT NULL REFERENCES "api2_keys"("id") ON DELETE CASCADE,
    "endpoint" TEXT NOT NULL,
    "method" TEXT NOT NULL,
    "timestamp" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "response_status" INTEGER NOT NULL,
    "response_time_ms" INTEGER, -- Response time in milliseconds
    "ip_address" INET,
    "user_agent" TEXT,
    "request_size" INTEGER, -- Request body size in bytes
    "response_size" INTEGER, -- Response body size in bytes
    "error_message" TEXT -- Error message if request failed
);

-- Daily Usage Summary table for API2 - Aggregate daily usage per key
CREATE TABLE IF NOT EXISTS "api2_daily_usage_summary" (
    "id" BIGSERIAL PRIMARY KEY,
    "key_id" INTEGER NOT NULL REFERENCES "api2_keys"("id") ON DELETE CASCADE,
    "date" DATE NOT NULL,
    "total_requests" INTEGER DEFAULT 0,
    "successful_requests" INTEGER DEFAULT 0,
    "failed_requests" INTEGER DEFAULT 0,
    "total_response_time_ms" BIGINT DEFAULT 0,
    "avg_response_time_ms" DECIMAL(10,2),
    "endpoints_used" TEXT[], -- Array of unique endpoints used
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE("key_id", "date")
);

-- Rate Limiting table for API2 - Track rate limiting windows
CREATE TABLE IF NOT EXISTS "api2_rate_limit_windows" (
    "id" BIGSERIAL PRIMARY KEY,
    "key_id" INTEGER NOT NULL REFERENCES "api2_keys"("id") ON DELETE CASCADE,
    "window_start" TIMESTAMP NOT NULL,
    "window_type" TEXT NOT NULL, -- 'minute', 'hour', 'day'
    "request_count" INTEGER DEFAULT 0,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE("key_id", "window_start", "window_type")
);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS "idx_api2_keys_key_hash" ON "api2_keys"("key_hash");
CREATE INDEX IF NOT EXISTS "idx_api2_keys_user_id" ON "api2_keys"("user_id");
CREATE INDEX IF NOT EXISTS "idx_api2_keys_is_active" ON "api2_keys"("is_active");
CREATE INDEX IF NOT EXISTS "idx_api2_keys_expires_at" ON "api2_keys"("expires_at");
CREATE INDEX IF NOT EXISTS "idx_api2_keys_tier" ON "api2_keys"("tier");

CREATE INDEX IF NOT EXISTS "idx_api2_usage_key_id" ON "api2_usage"("key_id");
CREATE INDEX IF NOT EXISTS "idx_api2_usage_timestamp" ON "api2_usage"("timestamp");
CREATE INDEX IF NOT EXISTS "idx_api2_usage_endpoint" ON "api2_usage"("endpoint");
CREATE INDEX IF NOT EXISTS "idx_api2_usage_response_status" ON "api2_usage"("response_status");

CREATE INDEX IF NOT EXISTS "idx_api2_daily_usage_key_id" ON "api2_daily_usage_summary"("key_id");
CREATE INDEX IF NOT EXISTS "idx_api2_daily_usage_date" ON "api2_daily_usage_summary"("date");
CREATE INDEX IF NOT EXISTS "idx_api2_daily_usage_key_date" ON "api2_daily_usage_summary"("key_id", "date");

CREATE INDEX IF NOT EXISTS "idx_api2_rate_limit_key_id" ON "api2_rate_limit_windows"("key_id");
CREATE INDEX IF NOT EXISTS "idx_api2_rate_limit_window" ON "api2_rate_limit_windows"("key_id", "window_start", "window_type");

-- Create function to update daily usage summary for API2
CREATE OR REPLACE FUNCTION update_api2_daily_usage_summary()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO api2_daily_usage_summary (key_id, date, total_requests, successful_requests, failed_requests, total_response_time_ms, endpoints_used, updated_at)
    VALUES (
        NEW.key_id,
        NEW.timestamp::date,
        1,
        CASE WHEN NEW.response_status < 400 THEN 1 ELSE 0 END,
        CASE WHEN NEW.response_status >= 400 THEN 1 ELSE 0 END,
        COALESCE(NEW.response_time_ms, 0),
        ARRAY[NEW.endpoint],
        CURRENT_TIMESTAMP
    )
    ON CONFLICT (key_id, date) DO UPDATE SET
        total_requests = api2_daily_usage_summary.total_requests + 1,
        successful_requests = api2_daily_usage_summary.successful_requests + 
            CASE WHEN NEW.response_status < 400 THEN 1 ELSE 0 END,
        failed_requests = api2_daily_usage_summary.failed_requests + 
            CASE WHEN NEW.response_status >= 400 THEN 1 ELSE 0 END,
        total_response_time_ms = api2_daily_usage_summary.total_response_time_ms + COALESCE(NEW.response_time_ms, 0),
        avg_response_time_ms = (api2_daily_usage_summary.total_response_time_ms + COALESCE(NEW.response_time_ms, 0))::decimal / 
            (api2_daily_usage_summary.total_requests + 1),
        endpoints_used = array_append(
            CASE WHEN NEW.endpoint = ANY(api2_daily_usage_summary.endpoints_used) 
                 THEN api2_daily_usage_summary.endpoints_used 
                 ELSE api2_daily_usage_summary.endpoints_used 
            END, 
            CASE WHEN NEW.endpoint = ANY(api2_daily_usage_summary.endpoints_used) 
                 THEN NULL 
                 ELSE NEW.endpoint 
            END
        ),
        updated_at = CURRENT_TIMESTAMP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update daily usage summary for API2
DROP TRIGGER IF EXISTS trigger_update_api2_daily_usage ON api2_usage;
CREATE TRIGGER trigger_update_api2_daily_usage
    AFTER INSERT ON api2_usage
    FOR EACH ROW
    EXECUTE FUNCTION update_api2_daily_usage_summary();

COMMIT;

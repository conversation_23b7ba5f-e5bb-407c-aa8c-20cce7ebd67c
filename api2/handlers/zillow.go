package handlers

import (
	"net/http"
	"net/url"
	"strconv"

	"propbolt-api2/models"
	"propbolt-api2/zillow"

	"github.com/gin-gonic/gin"
)

// ZillowHandler handles Zillow-related API endpoints
type ZillowHandler struct{}

// NewZillowHandler creates a new ZillowHandler
func NewZillowHandler() *ZillowHandler {
	return &ZillowHandler{}
}

// SearchForSale handles property search for sale
func (h *<PERSON><PERSON>w<PERSON>andler) SearchForSale(c *gin.Context) {
	// Extract query parameters
	pagination, _ := strconv.Atoi(c.<PERSON><PERSON>("page", "1"))
	zoomValue, _ := strconv.Atoi(c.<PERSON>("zoom", "11"))
	searchValue := c.Query("search")

	// Parse map bounds
	mapBounds, err := parseMapBounds(c)
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"error": "Invalid map bounds",
			"message": err.Error(),
		})
		return
	}

	// Parse filter parameters
	filter := parseFilterForSale(c)

	// Get proxy URL if configured
	var proxyURL *url.URL
	if proxyStr := c.Query("proxy"); proxyStr != "" {
		proxyURL, _ = url.Parse(proxyStr)
	}

	// Call Zillow search
	listResults, mapResults, err := filter.ForSale(pagination, zoomValue, searchValue, mapBounds, proxyURL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Search failed",
			"message": err.Error(),
		})
		return
	}

	// Return results
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"list_results": listResults,
			"map_results":  mapResults,
			"pagination":   pagination,
			"total_results": len(listResults),
		},
		"meta": gin.H{
			"search_term": searchValue,
			"zoom_level":  zoomValue,
			"page":        pagination,
		},
	})
}

// SearchForRent handles property search for rent
func (h *ZillowHandler) SearchForRent(c *gin.Context) {
	// Extract query parameters
	pagination, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	zoomValue, _ := strconv.Atoi(c.DefaultQuery("zoom", "11"))
	searchValue := c.Query("search")

	// Parse map bounds
	mapBounds, err := parseMapBounds(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid map bounds",
			"message": err.Error(),
		})
		return
	}

	// Parse filter parameters
	filter := parseFilterForRent(c)

	// Get proxy URL if configured
	var proxyURL *url.URL
	if proxyStr := c.Query("proxy"); proxyStr != "" {
		proxyURL, _ = url.Parse(proxyStr)
	}

	// Call Zillow search
	listResults, mapResults, err := filter.ForRent(pagination, zoomValue, searchValue, mapBounds, proxyURL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Search failed",
			"message": err.Error(),
		})
		return
	}

	// Return results
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"list_results": listResults,
			"map_results":  mapResults,
			"pagination":   pagination,
			"total_results": len(listResults),
		},
		"meta": gin.H{
			"search_term": searchValue,
			"zoom_level":  zoomValue,
			"page":        pagination,
		},
	})
}

// SearchSold handles sold property search
func (h *ZillowHandler) SearchSold(c *gin.Context) {
	// Extract query parameters
	pagination, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	zoomValue, _ := strconv.Atoi(c.DefaultQuery("zoom", "11"))
	searchValue := c.Query("search")

	// Parse map bounds
	mapBounds, err := parseMapBounds(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid map bounds",
			"message": err.Error(),
		})
		return
	}

	// Parse filter parameters
	filter := parseFilterSold(c)

	// Get proxy URL if configured
	var proxyURL *url.URL
	if proxyStr := c.Query("proxy"); proxyStr != "" {
		proxyURL, _ = url.Parse(proxyStr)
	}

	// Call Zillow search
	listResults, mapResults, err := filter.Sold(pagination, zoomValue, searchValue, mapBounds, proxyURL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Search failed",
			"message": err.Error(),
		})
		return
	}

	// Return results
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"list_results": listResults,
			"map_results":  mapResults,
			"pagination":   pagination,
			"total_results": len(listResults),
		},
		"meta": gin.H{
			"search_term": searchValue,
			"zoom_level":  zoomValue,
			"page":        pagination,
		},
	})
}

// GetPropertyDetails handles property details by URL
func (h *ZillowHandler) GetPropertyDetails(c *gin.Context) {
	propertyURL := c.Query("url")
	if propertyURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing property URL",
			"message": "Please provide a property URL in the 'url' query parameter",
		})
		return
	}

	// Get property type (1 for home, 2 for apartment)
	propertyType, _ := strconv.Atoi(c.DefaultQuery("type", "1"))

	// Get proxy URL if configured
	var proxyURL *url.URL
	if proxyStr := c.Query("proxy"); proxyStr != "" {
		proxyURL, _ = url.Parse(proxyStr)
	}

	if propertyType == 2 {
		// Apartment property
		floorPlans, _, err := zillow.FromPropertyURL(2, propertyURL, proxyURL)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to get apartment details",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"property_type": "apartment",
				"floor_plans":   floorPlans,
				"url":          propertyURL,
			},
		})
	} else {
		// Home property
		propertyInfo, _, err := zillow.FromPropertyURL(1, propertyURL, proxyURL)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to get property details",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"property_type": "home",
				"property_info": propertyInfo,
				"url":          propertyURL,
			},
		})
	}
}

// Helper functions for parsing request parameters

// parseMapBounds parses map bounds from query parameters
func parseMapBounds(c *gin.Context) (zillow.MapBounds, error) {
	var bounds zillow.MapBounds

	// Parse northeast coordinates
	neLat, err := strconv.ParseFloat(c.Query("ne_lat"), 64)
	if err != nil {
		return bounds, err
	}
	neLng, err := strconv.ParseFloat(c.Query("ne_lng"), 64)
	if err != nil {
		return bounds, err
	}

	// Parse southwest coordinates
	swLat, err := strconv.ParseFloat(c.Query("sw_lat"), 64)
	if err != nil {
		return bounds, err
	}
	swLng, err := strconv.ParseFloat(c.Query("sw_lng"), 64)
	if err != nil {
		return bounds, err
	}

	bounds.Ne = zillow.Coordinates{
		Latitude:  neLat,
		Longitude: neLng,
	}
	bounds.Sw = zillow.Coordinates{
		Latitude:  swLat,
		Longitude: swLng,
	}

	return bounds, nil
}

// parseFilterForSale parses filter parameters for sale search
func parseFilterForSale(c *gin.Context) zillow.Filter {
	var filter zillow.Filter

	// Parse beds
	minBeds, _ := strconv.Atoi(c.DefaultQuery("min_beds", "0"))
	maxBeds, _ := strconv.Atoi(c.DefaultQuery("max_beds", "0"))
	filter.Beds = zillow.MinMax{Min: minBeds, Max: maxBeds}

	// Parse bathrooms
	minBaths, _ := strconv.Atoi(c.DefaultQuery("min_baths", "0"))
	maxBaths, _ := strconv.Atoi(c.DefaultQuery("max_baths", "0"))
	filter.Bathrooms = zillow.MinMax{Min: minBaths, Max: maxBaths}

	// Parse price
	minPrice, _ := strconv.Atoi(c.DefaultQuery("min_price", "0"))
	maxPrice, _ := strconv.Atoi(c.DefaultQuery("max_price", "0"))
	filter.Price = zillow.MinMax{Min: minPrice, Max: maxPrice}

	return filter
}

// parseFilterForRent parses filter parameters for rent search
func parseFilterForRent(c *gin.Context) zillow.Filter {
	// Same as for sale for now, but could be extended with rent-specific filters
	return parseFilterForSale(c)
}

// parseFilterSold parses filter parameters for sold search
func parseFilterSold(c *gin.Context) zillow.Filter {
	// Same as for sale for now, but could be extended with sold-specific filters
	return parseFilterForSale(c)
}

// Autocomplete handles location autocomplete
func (h *ZillowHandler) Autocomplete(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing query parameter",
			"message": "Please provide a search query in the 'q' parameter",
		})
		return
	}

	// Get proxy URL if configured
	var proxyURL *url.URL
	if proxyStr := c.Query("proxy"); proxyStr != "" {
		proxyURL, _ = url.Parse(proxyStr)
	}

	// Call Zillow autocomplete
	results, err := zillow.Autocomplete(query, proxyURL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Autocomplete failed",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"query":   query,
			"results": results,
		},
	})
}

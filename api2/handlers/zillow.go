package handlers

import (
	"net/http"
	"net/url"
	"strconv"

	"propbolt-api2/search"
	"propbolt-api2/details"
	"propbolt-api2/autocomplete"

	"github.com/gin-gonic/gin"
)

// ZillowHandler handles Zillow-related API endpoints
type <PERSON>illow<PERSON><PERSON>ler struct{}

// NewZillowHandler creates a new ZillowHandler
func NewZillowHandler() *ZillowHandler {
	return &ZillowHandler{}
}

// SearchForSale handles property search for sale
func (h *ZillowHandler) SearchForSale(c *gin.Context) {
	// Required geographic bounds
	neLat, err := parseFloatParam(c, "neLat", 0)
	if err != nil || neLat == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "neLat parameter is required and must be a valid number",
		})
		return
	}

	neLong, err := parseFloatParam(c, "neLong", 0)
	if err != nil || neLong == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "neLong parameter is required and must be a valid number",
		})
		return
	}

	swLat, err := parseFloatParam(c, "swLat", 0)
	if err != nil || swLat == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "swLat parameter is required and must be a valid number",
		})
		return
	}

	swLong, err := parseFloatParam(c, "swLong", 0)
	if err != nil || swLong == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "swLong parameter is required and must be a valid number",
		})
		return
	}

	// Optional parameters with defaults
	pagination, err := parseIntParam(c, "page", 1)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	zoomValue, err := parseIntParam(c, "zoom", 10)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	if zoomValue < 1 || zoomValue > 20 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "zoom parameter must be between 1 and 20",
		})
		return
	}

	// Property type filters
	isAllHomes := parseBoolParam(c, "isAllHomes", true)
	isTownhouse := parseBoolParam(c, "isTownhouse", false)
	isMultiFamily := parseBoolParam(c, "isMultiFamily", false)
	isCondo := parseBoolParam(c, "isCondo", false)
	isLotLand := parseBoolParam(c, "isLotLand", false)
	isApartment := parseBoolParam(c, "isApartment", false)
	isManufactured := parseBoolParam(c, "isManufactured", false)
	isApartmentOrCondo := parseBoolParam(c, "isApartmentOrCondo", false)

	// School filters
	isElementarySchool := parseBoolParam(c, "isElementarySchool", false)
	isMiddleSchool := parseBoolParam(c, "isMiddleSchool", false)
	isHighSchool := parseBoolParam(c, "isHighSchool", false)
	isPublicSchool := parseBoolParam(c, "isPublicSchool", false)
	isPrivateSchool := parseBoolParam(c, "isPrivateSchool", false)
	isCharterSchool := parseBoolParam(c, "isCharterSchool", false)
	includeUnratedSchools := parseBoolParam(c, "includeUnratedSchools", false)

	// Price filters
	priceMin, err := parseIntParam(c, "priceMin", 0)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	priceMax, err := parseIntParam(c, "priceMax", 0)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	monthlyPaymentMin, err := parseIntParam(c, "monthlyPaymentMin", 0)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	monthlyPaymentMax, err := parseIntParam(c, "monthlyPaymentMax", 0)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get proxy URL if configured
	var proxyURL *url.URL
	if proxyStr := c.Query("proxy"); proxyStr != "" {
		proxyURL, _ = url.Parse(proxyStr)
	}

	// Call the search function
	listResults, mapResults, err := search.ForSale(
		pagination, zoomValue, neLat, neLong, swLat, swLong,
		isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool,
		isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools,
		isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment,
		isManufactured, isApartmentOrCondo,
		priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax,
		proxyURL,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Search failed",
			"message": err.Error(),
		})
		return
	}

	// Return results
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"list_results": listResults,
			"map_results":  mapResults,
			"total_count":  len(listResults),
		},
		"meta": gin.H{
			"page":        pagination,
			"zoom_level":  zoomValue,
		},
	})
}

// SearchForRent handles property search for rent
func (h *ZillowHandler) SearchForRent(c *gin.Context) {
	// Required geographic bounds
	neLat, err := parseFloatParam(c, "neLat", 0)
	if err != nil || neLat == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "neLat parameter is required and must be a valid number",
		})
		return
	}

	neLong, err := parseFloatParam(c, "neLong", 0)
	if err != nil || neLong == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "neLong parameter is required and must be a valid number",
		})
		return
	}

	swLat, err := parseFloatParam(c, "swLat", 0)
	if err != nil || swLat == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "swLat parameter is required and must be a valid number",
		})
		return
	}

	swLong, err := parseFloatParam(c, "swLong", 0)
	if err != nil || swLong == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "swLong parameter is required and must be a valid number",
		})
		return
	}

	// Optional parameters with defaults
	pagination, err := parseIntParam(c, "page", 1)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	zoomValue, err := parseIntParam(c, "zoom", 10)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	if zoomValue < 1 || zoomValue > 20 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "zoom parameter must be between 1 and 20",
		})
		return
	}

	// Get proxy URL if configured
	var proxyURL *url.URL
	if proxyStr := c.Query("proxy"); proxyStr != "" {
		proxyURL, _ = url.Parse(proxyStr)
	}

	// Call the search function
	listResults, mapResults, err := search.ForRent(
		pagination, zoomValue, neLat, neLong, swLat, swLong,
		proxyURL,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Search failed",
			"message": err.Error(),
		})
		return
	}

	// Return results
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"list_results": listResults,
			"map_results":  mapResults,
			"total_count":  len(listResults),
		},
		"meta": gin.H{
			"page":        pagination,
			"zoom_level":  zoomValue,
		},
	})
}

// SearchSold handles sold property search
func (h *ZillowHandler) SearchSold(c *gin.Context) {
	// Required geographic bounds
	neLat, err := parseFloatParam(c, "neLat", 0)
	if err != nil || neLat == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "neLat parameter is required and must be a valid number",
		})
		return
	}

	neLong, err := parseFloatParam(c, "neLong", 0)
	if err != nil || neLong == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "neLong parameter is required and must be a valid number",
		})
		return
	}

	swLat, err := parseFloatParam(c, "swLat", 0)
	if err != nil || swLat == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "swLat parameter is required and must be a valid number",
		})
		return
	}

	swLong, err := parseFloatParam(c, "swLong", 0)
	if err != nil || swLong == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "swLong parameter is required and must be a valid number",
		})
		return
	}

	// Optional parameters with defaults
	pagination, err := parseIntParam(c, "page", 1)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	zoomValue, err := parseIntParam(c, "zoom", 10)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	if zoomValue < 1 || zoomValue > 20 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "zoom parameter must be between 1 and 20",
		})
		return
	}

	// Get proxy URL if configured
	var proxyURL *url.URL
	if proxyStr := c.Query("proxy"); proxyStr != "" {
		proxyURL, _ = url.Parse(proxyStr)
	}

	// Call the search function
	listResults, mapResults, err := search.Sold(
		pagination, zoomValue, neLat, neLong, swLat, swLong,
		proxyURL,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Search failed",
			"message": err.Error(),
		})
		return
	}

	// Return results
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"list_results": listResults,
			"map_results":  mapResults,
			"total_count":  len(listResults),
		},
		"meta": gin.H{
			"page":        pagination,
			"zoom_level":  zoomValue,
		},
	})
}

// GetPropertyDetails handles property details by URL, ID, or address
func (h *ZillowHandler) GetPropertyDetails(c *gin.Context) {
	propertyIDStr := c.Query("id")
	propertyURL := c.Query("url")
	homeAddress := c.Query("address")

	// Get proxy URL if configured
	var proxyURL *url.URL
	if proxyStr := c.Query("proxy"); proxyStr != "" {
		proxyURL, _ = url.Parse(proxyStr)
	}

	var property details.PropertyInfo
	var err error

	if propertyIDStr != "" {
		propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid property ID",
				"message": err.Error(),
			})
			return
		}
		property, err = details.FromPropertyID(propertyID, proxyURL)
	} else if propertyURL != "" {
		property, err = details.FromPropertyURL(propertyURL, proxyURL)
	} else if homeAddress != "" {
		property, err = details.FromHomeAddress(homeAddress, proxyURL)
	} else {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing parameter",
			"message": "Either Property ID, URL, or Address is required",
		})
		return
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get property details",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"property": property,
		},
	})
}

// Helper functions for parsing request parameters

// Helper function to parse boolean parameters
func parseBoolParam(c *gin.Context, param string, defaultValue bool) bool {
	value := c.Query(param)
	if value == "" {
		return defaultValue
	}
	result, err := strconv.ParseBool(value)
	if err != nil {
		return defaultValue
	}
	return result
}

// Helper function to parse integer parameters
func parseIntParam(c *gin.Context, param string, defaultValue int) (int, error) {
	value := c.Query(param)
	if value == "" {
		return defaultValue, nil
	}
	result, err := strconv.Atoi(value)
	if err != nil {
		return defaultValue, err
	}
	return result, nil
}

// Helper function to parse float parameters
func parseFloatParam(c *gin.Context, param string, defaultValue float64) (float64, error) {
	value := c.Query(param)
	if value == "" {
		return defaultValue, nil
	}
	result, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return defaultValue, err
	}
	return result, nil
}

// Autocomplete handles location autocomplete
func (h *ZillowHandler) Autocomplete(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing query parameter",
			"message": "Please provide a search query in the 'q' parameter",
		})
		return
	}

	// Get proxy URL if configured
	var proxyURL *url.URL
	if proxyStr := c.Query("proxy"); proxyStr != "" {
		proxyURL, _ = url.Parse(proxyStr)
	}

	// Call Zillow autocomplete
	suggestions, err := autocomplete.GetSuggestions(query, proxyURL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Autocomplete failed",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"query":   query,
			"results": suggestions,
		},
	})
}

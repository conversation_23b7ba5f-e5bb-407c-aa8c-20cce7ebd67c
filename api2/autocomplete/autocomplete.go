package autocomplete

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/google/uuid"
	"propbolt-api2/utils"
)

// AutocompleteResult represents a single autocomplete suggestion
type AutocompleteResult struct {
	ID        string   `json:"id,omitempty"`
	RegionID  int      `json:"regionId,omitempty"`
	SubType   string   `json:"subType,omitempty"`
	RegionIDs []string `json:"regionIds,omitempty"`
	Display   string   `json:"display,omitempty"`
	Type      string   `json:"type,omitempty"`
}

// AutocompleteResponse represents the full autocomplete response
type AutocompleteResponse struct {
	Results   []AutocompleteResult `json:"results"`
	RequestID string               `json:"requestId"`
}

// RegionBounds represents geographic boundaries for a region
type RegionBounds struct {
	North float64 `json:"north"`
	South float64 `json:"south"`
	East  float64 `json:"east"`
	West  float64 `json:"west"`
}

// RegionInfo contains detailed information about a region
type RegionInfo struct {
	RegionID      int    `json:"regionId"`
	RegionType    int    `json:"regionType"`
	RegionName    string `json:"regionName"`
	DisplayName   string `json:"displayName"`
	IsPointRegion bool   `json:"isPointRegion"`
}

// RegionDetailsResponse contains region details and bounds
type RegionDetailsResponse struct {
	RegionInfo   []RegionInfo `json:"regionInfo"`
	RegionBounds RegionBounds `json:"regionBounds"`
	RequestID    int          `json:"requestId"`
}

// Internal structures for GraphQL requests
type autocompleteRequest struct {
	OperationName string    `json:"operationName"`
	Query         string    `json:"query"`
	Variables     variables `json:"variables"`
}

type variables struct {
	Query        string       `json:"query"`
	ResultType   []string     `json:"resultType"`
	QueryOptions queryOptions `json:"queryOptions"`
}

type queryOptions struct {
	UserSearchContext string       `json:"userSearchContext"`
	MaxResults        int          `json:"maxResults"`
	UserIdentity      userIdentity `json:"userIdentity"`
	UserLocation      userLocation `json:"userLocation"`
}

type userIdentity struct {
	AbKey string `json:"abKey"`
}

type userLocation struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// Internal response structures
type graphqlResponse struct {
	Data graphqlData `json:"data"`
}

type graphqlData struct {
	SearchAssistanceResult searchAssistanceResult `json:"searchAssistanceResult"`
}

type searchAssistanceResult struct {
	Results   []rawResult `json:"results"`
	RequestID string      `json:"requestId"`
}

type rawResult struct {
	ID        string   `json:"id,omitempty"`
	RegionID  int      `json:"regionId,omitempty"`
	SubType   string   `json:"subType,omitempty"`
	RegionIDs []string `json:"regionIds,omitempty"`
	Typename  string   `json:"__typename,omitempty"`
}

// GetSuggestions returns autocomplete suggestions for a given query
func GetSuggestions(query string, proxyURL *url.URL) (AutocompleteResponse, error) {
	return getSuggestionsWithRetry(query, proxyURL, utils.DefaultRetryConfig())
}

// GetSuggestionsWithRetry returns autocomplete suggestions with custom retry configuration
func GetSuggestionsWithRetry(query string, proxyURL *url.URL, retryConfig utils.RetryConfig) (AutocompleteResponse, error) {
	return getSuggestionsWithRetry(query, proxyURL, retryConfig)
}

func getSuggestionsWithRetry(query string, proxyURL *url.URL, retryConfig utils.RetryConfig) (AutocompleteResponse, error) {
	result, retryResult := utils.WithRetry(func() (AutocompleteResponse, error) {
		return getSuggestions(query, proxyURL)
	}, retryConfig)

	if !retryResult.Success {
		return AutocompleteResponse{}, fmt.Errorf("failed after %d attempts: %w", retryResult.Attempts, retryResult.LastError)
	}

	return result, nil
}

func getSuggestions(query string, proxyURL *url.URL) (AutocompleteResponse, error) {
	id := uuid.New()
	
	// GraphQL query for autocomplete
	graphqlQuery := `query getAutocompleteResults($query: String!, $queryOptions: SearchAssistanceQueryOptions, $resultType: [SearchAssistanceResultType]) {
		searchAssistanceResult: zgsAutocompleteRequest(
			query: $query
			queryOptions: $queryOptions
			resultType: $resultType
		) {
			requestId
			results {
				__typename
				id
				...RegionResultFields
				...SemanticResultFields
				...RentalCommunityResultFields
				...SchoolResultFields
				...BuilderCommunityResultFields
			}
			__typename
		}
	}

	fragment RegionResultFields on SearchAssistanceRegionResult {
		regionId
		subType
		__typename
	}

	fragment SchoolResultFields on SearchAssistanceSchoolResult {
		id
		schoolDistrictId
		schoolId
		__typename
	}

	fragment SemanticResultFields on SearchAssistanceSemanticResult {
		nearMe
		regionIds
		regionTypes
		regionDisplayIds
		queryResolutionStatus
		viewLatitudeDelta
		__typename
	}

	fragment RentalCommunityResultFields on SearchAssistanceRentalCommunityResult {
		location {
			latitude
			longitude
			__typename
		}
		__typename
	}

	fragment BuilderCommunityResultFields on SearchAssistanceBuilderCommunityResult {
		plid
		__typename
	}`

	searchReq := autocompleteRequest{
		OperationName: "getAutocompleteResults",
		Query:         graphqlQuery,
		Variables: variables{
			Query:      query,
			ResultType: []string{"REGIONS", "FORSALE", "RENTALS", "SOLD", "COMMUNITIES", "BUILDER_COMMUNITIES"},
			QueryOptions: queryOptions{
				UserSearchContext: "FOR_SALE",
				MaxResults:        10,
				UserIdentity: userIdentity{
					AbKey: id.String(),
				},
				UserLocation: userLocation{
					Latitude:  33.83513618052438,
					Longitude: -96.8510195,
				},
			},
		},
	}

	rawData, err := json.Marshal(searchReq)
	if err != nil {
		return AutocompleteResponse{}, err
	}

	req, err := http.NewRequest("POST", "https://www.zillow.com/graphql", bytes.NewReader(rawData))
	if err != nil {
		return AutocompleteResponse{}, err
	}

	// Set headers for GraphQL request
	utils.SetJSONHeaders(req)
	req.Header.Set("x-caller-id", "hops-homepage")

	client := utils.CreateHTTPClient(proxyURL, time.Minute)
	resp, err := client.Do(req)
	if err != nil {
		return AutocompleteResponse{}, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return AutocompleteResponse{}, err
	}

	if resp.StatusCode != 200 {
		return AutocompleteResponse{}, fmt.Errorf("status: %d headers: %+v", resp.StatusCode, resp.Header)
	}

	var respData graphqlResponse
	if err := json.Unmarshal(body, &respData); err != nil {
		return AutocompleteResponse{}, err
	}

	// Convert raw results to our format
	results := make([]AutocompleteResult, len(respData.Data.SearchAssistanceResult.Results))
	for i, raw := range respData.Data.SearchAssistanceResult.Results {
		results[i] = AutocompleteResult{
			ID:        raw.ID,
			RegionID:  raw.RegionID,
			SubType:   raw.SubType,
			RegionIDs: raw.RegionIDs,
			Type:      raw.Typename,
		}
	}

	return AutocompleteResponse{
		Results:   results,
		RequestID: respData.Data.SearchAssistanceResult.RequestID,
	}, nil
}

// GetRegionDetails returns detailed information and bounds for a specific region ID
func GetRegionDetails(regionID int, proxyURL *url.URL) (RegionDetailsResponse, error) {
	return getRegionDetailsWithRetry(regionID, proxyURL, utils.DefaultRetryConfig())
}

// GetRegionDetailsWithRetry returns region details with custom retry configuration
func GetRegionDetailsWithRetry(regionID int, proxyURL *url.URL, retryConfig utils.RetryConfig) (RegionDetailsResponse, error) {
	return getRegionDetailsWithRetry(regionID, proxyURL, retryConfig)
}

func getRegionDetailsWithRetry(regionID int, proxyURL *url.URL, retryConfig utils.RetryConfig) (RegionDetailsResponse, error) {
	result, retryResult := utils.WithRetry(func() (RegionDetailsResponse, error) {
		return getRegionDetails(regionID, proxyURL)
	}, retryConfig)

	if !retryResult.Success {
		return RegionDetailsResponse{}, fmt.Errorf("failed after %d attempts: %w", retryResult.Attempts, retryResult.LastError)
	}

	return result, nil
}

func getRegionDetails(regionID int, proxyURL *url.URL) (RegionDetailsResponse, error) {
	// Create the request payload for region details
	rawInput := `{"searchQueryState":{"pagination":{},"isMapVisible":true,"mapBounds":{"west":-81.28426248920138,"east":-74.85726053607638,"south":33.384359500366614,"north":36.86965680294794},"regionSelection":[{"regionId":%d}],"filterState":{"isForRent":{"value":true},"isForSaleByAgent":{"value":false},"isForSaleByOwner":{"value":false},"isNewConstruction":{"value":false},"isComingSoon":{"value":false},"isAuction":{"value":false},"isForSaleForeclosure":{"value":false},"isTownhouse":{"value":false},"isMultiFamily":{"value":false},"isCondo":{"value":false},"isLotLand":{"value":false},"isApartment":{"value":false},"isManufactured":{"value":false},"isApartmentOrCondo":{"value":false}},"isListVisible":true,"mapZoom":8},"wants":{"regionResults":["regionResults"]},"requestId":9,"isDebugRequest":false}`
	rawData := fmt.Sprintf(rawInput, regionID)

	req, err := http.NewRequest("PUT", "https://www.zillow.com/async-create-search-page-state", strings.NewReader(rawData))
	if err != nil {
		return RegionDetailsResponse{}, err
	}

	// Set headers for the request
	utils.SetJSONHeaders(req)
	req.Header.Set("x-caller-id", "hops-homepage")

	client := utils.CreateHTTPClient(proxyURL, time.Minute)
	resp, err := client.Do(req)
	if err != nil {
		return RegionDetailsResponse{}, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return RegionDetailsResponse{}, err
	}

	if resp.StatusCode != 200 {
		return RegionDetailsResponse{}, fmt.Errorf("status: %d headers: %+v", resp.StatusCode, resp.Header)
	}

	// Parse the response
	var regionResponse struct {
		RegionState struct {
			RegionInfo   []RegionInfo `json:"regionInfo"`
			RegionBounds RegionBounds `json:"regionBounds"`
		} `json:"regionState"`
		RequestID int `json:"requestId"`
	}

	if err := json.Unmarshal(body, &regionResponse); err != nil {
		return RegionDetailsResponse{}, err
	}

	return RegionDetailsResponse{
		RegionInfo:   regionResponse.RegionState.RegionInfo,
		RegionBounds: regionResponse.RegionState.RegionBounds,
		RequestID:    regionResponse.RequestID,
	}, nil
}

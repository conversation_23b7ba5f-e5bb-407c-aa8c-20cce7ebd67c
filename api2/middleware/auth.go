package middleware

import (
	"database/sql"
	"net/http"
	"strconv"
	"strings"
	"time"

	"../models"

	"github.com/gin-gonic/gin"
)

// APIKeyMiddleware validates API keys and enforces rate limiting
func APIKeyMiddleware(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip authentication for health check and documentation endpoints
		path := c.Request.URL.Path
		if isPublicEndpoint(path) {
			c.Next()
			return
		}

		// Extract API key from header
		apiKey := extractAPIKey(c)
		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "API key required",
				"message": "Please provide a valid API key in the X-API-Key header or Authorization header",
			})
			c.Abort()
			return
		}

		// Validate API key
		keyModel, err := models.ValidateAPIKey(db, apiKey)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid API key",
				"message": err.<PERSON>rror(),
			})
			c.Abort()
			return
		}

		// Check rate limits
		if exceeded, err := checkRateLimits(db, keyModel); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Rate limit check failed",
				"message": err.Error(),
			})
			c.Abort()
			return
		} else if exceeded {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded",
				"message": "You have exceeded your API rate limit",
				"retry_after": "60", // seconds
			})
			c.Abort()
			return
		}

		// Update last used timestamp
		go func() {
			keyModel.UpdateLastUsed(db)
		}()

		// Store API key info in context for use in handlers
		c.Set("api_key", keyModel)
		c.Set("api_key_id", keyModel.ID)
		c.Set("user_id", keyModel.UserID)
		c.Set("api_tier", keyModel.Tier)

		c.Next()

		// Log usage after request completion
		go func() {
			logAPIUsage(db, keyModel, c)
		}()
	}
}

// extractAPIKey extracts the API key from request headers
func extractAPIKey(c *gin.Context) string {
	// Try X-API-Key header first
	apiKey := c.GetHeader("X-API-Key")
	if apiKey != "" {
		return apiKey
	}

	// Try Authorization header with Bearer token
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
		return strings.TrimPrefix(authHeader, "Bearer ")
	}

	return ""
}

// isPublicEndpoint checks if the endpoint doesn't require authentication
func isPublicEndpoint(path string) bool {
	publicPaths := []string{
		"/health",
		"/",
		"/docs",
		"/openapi.json",
		"/swagger",
	}

	for _, publicPath := range publicPaths {
		if strings.HasPrefix(path, publicPath) {
			return true
		}
	}

	return false
}

// checkRateLimits checks both per-minute and per-day rate limits
func checkRateLimits(db *sql.DB, key *models.APIKey) (bool, error) {
	// Check per-minute rate limit
	exceeded, err := models.CheckRateLimit(db, key.ID, "minute", key.RequestsPerMinute)
	if err != nil {
		return false, err
	}
	if exceeded {
		return true, nil
	}

	// Check per-day rate limit
	exceeded, err = models.CheckRateLimit(db, key.ID, "day", key.RequestsPerDay)
	if err != nil {
		return false, err
	}
	if exceeded {
		return true, nil
	}

	return false, nil
}

// logAPIUsage logs the API request for analytics and billing
func logAPIUsage(db *sql.DB, key *models.APIKey, c *gin.Context) {
	// Calculate response time if available
	var responseTime *int
	if startTime, exists := c.Get("start_time"); exists {
		if start, ok := startTime.(time.Time); ok {
			duration := int(time.Since(start).Milliseconds())
			responseTime = &duration
		}
	}

	// Get response size if available
	var responseSize *int
	if size := c.Writer.Size(); size > 0 {
		responseSize = &size
	}

	// Get request size
	var requestSize *int
	if c.Request.ContentLength > 0 {
		size := int(c.Request.ContentLength)
		requestSize = &size
	}

	// Get IP address
	clientIP := c.ClientIP()
	var ipAddress *string
	if clientIP != "" {
		ipAddress = &clientIP
	}

	// Get User-Agent
	userAgent := c.GetHeader("User-Agent")
	var ua *string
	if userAgent != "" {
		ua = &userAgent
	}

	// Get error message if request failed
	var errorMessage *string
	if c.Writer.Status() >= 400 {
		if errors, exists := c.Get("errors"); exists {
			if errStr, ok := errors.(string); ok {
				errorMessage = &errStr
			}
		}
	}

	usage := &models.APIUsage{
		KeyID:          key.ID,
		Endpoint:       c.Request.URL.Path,
		Method:         c.Request.Method,
		ResponseStatus: c.Writer.Status(),
		ResponseTimeMs: responseTime,
		IPAddress:      ipAddress,
		UserAgent:      ua,
		RequestSize:    requestSize,
		ResponseSize:   responseSize,
		ErrorMessage:   errorMessage,
	}

	if err := models.LogUsage(db, usage); err != nil {
		// Log error but don't fail the request
		// In production, you might want to use a proper logging system
		println("Failed to log API usage:", err.Error())
	}
}

// TimingMiddleware adds request timing
func TimingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		c.Set("start_time", start)
		c.Next()
	}
}

// CORSMiddleware handles CORS headers
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-API-Key")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// ErrorHandlingMiddleware handles panics and errors
func ErrorHandlingMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			c.Set("errors", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Internal server error",
				"message": "An unexpected error occurred",
			})
		}
		c.AbortWithStatus(http.StatusInternalServerError)
	})
}

package details

import (
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "net/url"
    "time"

    "propbolt-api2/utils"
)

func FromPropertyURL(propertyURL string, proxyURL *url.URL) (PropertyInfo, error) {
    return FromPropertyURLWithRetry(propertyURL, proxyURL, utils.DefaultRetryConfig())
}

func FromPropertyURLWithRetry(propertyURL string, proxyURL *url.URL, retryConfig utils.RetryConfig) (PropertyInfo, error) {
    result, retryResult := utils.WithRetry(func() (PropertyInfo, error) {
        return fromPropertyURL(propertyURL, proxyURL)
    }, retryConfig)

    if !retryResult.Success {
        return PropertyInfo{}, fmt.Errorf("failed after %d attempts: %w", retryResult.Attempts, retryResult.LastError)
    }

    return result, nil
}

func FromPropertyID(propertyID int64, proxyURL *url.URL) (PropertyInfo, error) {
    return FromPropertyIDWithRetry(propertyID, proxyURL, utils.DefaultRetryConfig())
}

func FromPropertyIDWithRetry(propertyID int64, proxyURL *url.URL, retryConfig utils.RetryConfig) (PropertyInfo, error) {
    propertyURL := fmt.Sprintf("https://www.zillow.com/homedetails/any-title/%d_zpid/", propertyID)
    return FromPropertyURLWithRetry(propertyURL, proxyURL, retryConfig)
}

func FromHomeAddress(address string, proxyURL *url.URL) (PropertyInfo, error) {
    return FromHomeAddressWithRetry(address, proxyURL, utils.DefaultRetryConfig())
}

func FromHomeAddressWithRetry(address string, proxyURL *url.URL, retryConfig utils.RetryConfig) (PropertyInfo, error) {
    result, retryResult := utils.WithRetry(func() (PropertyInfo, error) {
        return fromHomeAddress(address, proxyURL)
    }, retryConfig)

    if !retryResult.Success {
        return PropertyInfo{}, fmt.Errorf("failed after %d attempts: %w", retryResult.Attempts, retryResult.LastError)
    }

    return result, nil
}

func fromHomeAddress(address string, proxyURL *url.URL) (PropertyInfo, error) {
    encodedAddress := url.QueryEscape(address)
    apiURL := fmt.Sprintf("https://www.zillowstatic.com/autocomplete/v3/suggestions?q=%s", encodedAddress)

    req, err := http.NewRequest("GET", apiURL, nil)
    if err != nil {
        return PropertyInfo{}, err
    }

    utils.SetRealisticHeaders(req)
    client := utils.CreateHTTPClient(proxyURL, 30*time.Second)

    resp, err := client.Do(req)
    if err != nil {
        return PropertyInfo{}, err
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        return PropertyInfo{}, fmt.Errorf("status: %d headers: %v", resp.StatusCode, resp.Header)
    }

    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return PropertyInfo{}, err
    }

    var autocompleteResponse struct {
        Results []struct {
            MetaData struct {
                Zpid int64 `json:"zpid"`
            } `json:"metaData"`
        } `json:"results"`
    }

    if err := json.Unmarshal(body, &autocompleteResponse); err != nil {
        return PropertyInfo{}, err
    }

    if len(autocompleteResponse.Results) == 0 {
        return PropertyInfo{}, fmt.Errorf("no results found for the address")
    }

    zpid := autocompleteResponse.Results[0].MetaData.Zpid
    return FromPropertyID(zpid, proxyURL)
}

// Function to get minimal property information by ID
func FromPropertyIDMinimal(propertyID int64, proxyURL *url.URL) (PropertyMinimalInfo, error) {
    property, err := FromPropertyID(propertyID, proxyURL)
    if err != nil {
        return PropertyMinimalInfo{}, err
    }
    return ToMinimalInfo(property), nil
}

// Function to get minimal property information by URL
func FromPropertyURLMinimal(propertyURL string, proxyURL *url.URL) (PropertyMinimalInfo, error) {
    property, err := FromPropertyURL(propertyURL, proxyURL)
    if err != nil {
        return PropertyMinimalInfo{}, err
    }
    return ToMinimalInfo(property), nil
}

// Function to get minimal property information by address
func FromHomeAddressMinimal(address string, proxyURL *url.URL) (PropertyMinimalInfo, error) {
    property, err := FromHomeAddress(address, proxyURL)
    if err != nil {
        return PropertyMinimalInfo{}, err
    }
    return ToMinimalInfo(property), nil
}

// Function to get only ResponsivePhotos by Property ID
func FromPropertyIDPhotos(propertyID int64, proxyURL *url.URL) (ImagesOnly, error) {
    property, err := FromPropertyID(propertyID, proxyURL)
    if err != nil {
        return ImagesOnly{}, err
    }
    return ToResponsivePhotos(property), nil
}

// Function to get only ResponsivePhotos by Property URL
func FromPropertyURLPhotos(propertyURL string, proxyURL *url.URL) (ImagesOnly, error) {
    property, err := FromPropertyURL(propertyURL, proxyURL)
    if err != nil {
        return ImagesOnly{}, err
    }
    return ToResponsivePhotos(property), nil
}

// Function to get only ResponsivePhotos by Property Address
func FromHomeAddressPhotos(address string, proxyURL *url.URL) (ImagesOnly, error) {
    property, err := FromHomeAddress(address, proxyURL)
    if err != nil {
        return ImagesOnly{}, err
    }
    return ToResponsivePhotos(property), nil
}

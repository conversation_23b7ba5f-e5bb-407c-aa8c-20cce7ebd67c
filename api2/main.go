package main

import (
	"fmt"
	"log"
	"net/http"
	"os"

	"propbolt-api2/database"
	"propbolt-api2/handlers"
	"propbolt-api2/middleware"

	"github.com/gin-gonic/gin"
	"github.com/rs/cors"
)

func main() {
	// Initialize database connection
	if err := database.InitDB(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.CloseDB()

	// Set Gin mode based on environment
	if os.Getenv("GIN_MODE") == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// Create Gin router
	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware())
	router.Use(middleware.TimingMiddleware())
	router.Use(middleware.ErrorHandlingMiddleware())

	// Add API key authentication middleware for protected routes
	db := database.GetDB()
	authMiddleware := middleware.APIKeyMiddleware(db)

	// Health check endpoint (public)
	router.GET("/health", func(c *gin.Context) {
		c.<PERSON>(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "PropBolt API2 - Zillow Service",
			"version": "1.0.0",
		})
	})

	// Root endpoint (public)
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "PropBolt API2 - Zillow Real Estate Data Service",
			"version": "1.0.0",
			"endpoints": gin.H{
				"health":           "/health",
				"search_for_sale":  "/zillow/search/for-sale",
				"search_for_rent":  "/zillow/search/for-rent",
				"search_sold":      "/zillow/search/sold",
				"property_details": "/zillow/property/details",
				"autocomplete":     "/zillow/autocomplete",
			},
			"documentation": "/docs",
		})
	})

	// Initialize handlers
	zillowHandler := handlers.NewZillowHandler()

	// Protected Zillow API routes
	zillowGroup := router.Group("/zillow")
	zillowGroup.Use(authMiddleware)
	{
		// Search endpoints
		searchGroup := zillowGroup.Group("/search")
		{
			searchGroup.GET("/for-sale", zillowHandler.SearchForSale)
			searchGroup.GET("/for-rent", zillowHandler.SearchForRent)
			searchGroup.GET("/sold", zillowHandler.SearchSold)
		}

		// Property details endpoint
		propertyGroup := zillowGroup.Group("/property")
		{
			propertyGroup.GET("/details", zillowHandler.GetPropertyDetails)
		}

		// Autocomplete endpoint
		zillowGroup.GET("/autocomplete", zillowHandler.Autocomplete)
	}

	// API documentation endpoint (public)
	router.GET("/docs", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"title":       "PropBolt API2 - Zillow Service",
			"description": "Real estate data service powered by Zillow scraping",
			"version":     "1.0.0",
			"base_url":    "https://api2.propbolt.com",
			"authentication": gin.H{
				"type":        "API Key",
				"header":      "X-API-Key",
				"description": "Provide your API key in the X-API-Key header",
			},
			"endpoints": gin.H{
				"search_for_sale": gin.H{
					"method":      "GET",
					"path":        "/zillow/search/for-sale",
					"description": "Search properties for sale",
					"parameters": gin.H{
						"page":      "Page number (default: 1)",
						"zoom":      "Map zoom level (default: 11)",
						"search":    "Search term (city, address, etc.)",
						"ne_lat":    "Northeast latitude (required)",
						"ne_lng":    "Northeast longitude (required)",
						"sw_lat":    "Southwest latitude (required)",
						"sw_lng":    "Southwest longitude (required)",
						"min_beds":  "Minimum bedrooms",
						"max_beds":  "Maximum bedrooms",
						"min_baths": "Minimum bathrooms",
						"max_baths": "Maximum bathrooms",
						"min_price": "Minimum price",
						"max_price": "Maximum price",
					},
				},
				"search_for_rent": gin.H{
					"method":      "GET",
					"path":        "/zillow/search/for-rent",
					"description": "Search properties for rent",
					"parameters": gin.H{
						"page":      "Page number (default: 1)",
						"zoom":      "Map zoom level (default: 11)",
						"search":    "Search term (city, address, etc.)",
						"ne_lat":    "Northeast latitude (required)",
						"ne_lng":    "Northeast longitude (required)",
						"sw_lat":    "Southwest latitude (required)",
						"sw_lng":    "Southwest longitude (required)",
						"min_beds":  "Minimum bedrooms",
						"max_beds":  "Maximum bedrooms",
						"min_baths": "Minimum bathrooms",
						"max_baths": "Maximum bathrooms",
						"min_price": "Minimum price",
						"max_price": "Maximum price",
					},
				},
				"search_sold": gin.H{
					"method":      "GET",
					"path":        "/zillow/search/sold",
					"description": "Search sold properties",
					"parameters": gin.H{
						"page":      "Page number (default: 1)",
						"zoom":      "Map zoom level (default: 11)",
						"search":    "Search term (city, address, etc.)",
						"ne_lat":    "Northeast latitude (required)",
						"ne_lng":    "Northeast longitude (required)",
						"sw_lat":    "Southwest latitude (required)",
						"sw_lng":    "Southwest longitude (required)",
						"min_beds":  "Minimum bedrooms",
						"max_beds":  "Maximum bedrooms",
						"min_baths": "Minimum bathrooms",
						"max_baths": "Maximum bathrooms",
						"min_price": "Minimum price",
						"max_price": "Maximum price",
					},
				},
				"property_details": gin.H{
					"method":      "GET",
					"path":        "/zillow/property/details",
					"description": "Get detailed property information",
					"parameters": gin.H{
						"url":  "Zillow property URL (required)",
						"type": "Property type: 1 for home, 2 for apartment (default: 1)",
					},
				},
				"autocomplete": gin.H{
					"method":      "GET",
					"path":        "/zillow/autocomplete",
					"description": "Get location autocomplete suggestions",
					"parameters": gin.H{
						"q": "Search query (required)",
					},
				},
			},
		})
	})

	// Setup CORS for non-Gin routes if needed
	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	})

	// Get port from environment or use default
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	// Create HTTP handler with CORS
	handler := c.Handler(router)

	// Start server
	fmt.Printf("🚀 PropBolt API2 Server started at http://localhost:%s\n", port)
	fmt.Printf("✅ Database connected successfully\n")
	fmt.Printf("🔐 API key authentication enabled\n")
	fmt.Printf("📋 Documentation available at /docs\n")
	fmt.Printf("🏠 Zillow endpoints available at /zillow/*\n")
	
	log.Fatal(http.ListenAndServe(":"+port, handler))
}

runtime: go122
service: api2

# Auto-scaling configuration
automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.6
  target_throughput_utilization: 0.6

# Environment variables
env_variables:
  GIN_MODE: release
  DB_HOST: /cloudsql/gold-braid-458901-v2:us-central1:propbolt-db
  DB_PORT: "5432"
  DB_USER: postgres
  DB_NAME: propbolt
  GOOGLE_CLOUD_PROJECT: gold-braid-458901-v2
  CLOUD_SQL_CONNECTION_NAME: gold-braid-458901-v2:us-central1:propbolt-db

# Resource limits
resources:
  cpu: 1
  memory_gb: 2
  disk_size_gb: 10

# Network settings
network:
  forwarded_ports:
    - 8080

# Health check configuration
readiness_check:
  path: "/health"
  check_interval_sec: 5
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2
  app_start_timeout_sec: 300

liveness_check:
  path: "/health"
  check_interval_sec: 30
  timeout_sec: 4
  failure_threshold: 4
  success_threshold: 2

# VPC connector for Cloud SQL
vpc_access_connector:
  name: projects/gold-braid-458901-v2/locations/us-central1/connectors/propbolt-connector

# Beta settings for Cloud SQL
beta_settings:
  cloud_sql_instances: gold-braid-458901-v2:us-central1:propbolt-db

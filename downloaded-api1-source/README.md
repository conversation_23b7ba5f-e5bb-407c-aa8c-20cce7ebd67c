
# PropBolt API1 Service

Real Estate Data API Service for `api1.propbolt.com` using Google Cloud Endpoints.

## Overview

The API1 service is a Google Cloud Endpoints-powered real estate data API that provides comprehensive property information, search capabilities, and analytics tools. It integrates the original Real Estate API SDK with PropBolt's enhanced authentication, rate limiting, and database management features.

## Architecture

- **Framework**: Flask with Google Cloud Endpoints
- **Authentication**: API key-based with database management
- **Database**: PostgreSQL (shared with other PropBolt services)
- **Deployment**: Google Cloud App Engine
- **Domain**: api1.propbolt.com

## Features

### Core API Endpoints

1. **Property Search** (`POST /v2/PropertySearch`) - Advanced property search and filtering
2. **Property Details** (`POST /v2/PropertyDetail`) - Comprehensive property information
3. **Bulk Property Details** (`POST /v2/PropertyDetailBulk`) - Retrieve up to 1000 properties
4. **Property Parcel** (`POST /v1/PropertyParcel`) - Property boundaries in GeoJSON
5. **Property Comparables v2** (`POST /v2/PropertyComps`) - Basic comparables
6. **Property Comparables v3** (`POST /v3/PropertyComps`) - Advanced comparables
7. **AutoComplete** (`POST /v2/AutoComplete`) - Address autocomplete
8. **Address Verification** (`POST /v2/AddressVerification`) - Verify addresses
9. **PropGPT** (`POST /v2/PropGPT`) - AI-powered property search
10. **CSV Builder** (`POST /v2/CSVBuilder`) - Generate CSV exports
11. **Property AVM** (`POST /v2/PropertyAvm`) - Automated valuations
12. **Property Liens** (`POST /v2/Reports/PropertyLiens`) - Lien information
13. **Property Mapping** (`POST /v2/PropertyMapping`) - Map pins for PropTech apps

### Enhanced Features

- **API Key Management**: Database-driven API key authentication with rate limiting
- **Usage Tracking**: Comprehensive usage analytics and quota management
- **Error Handling**: Robust error handling with detailed error messages
- **Health Monitoring**: Health check endpoints for monitoring
- **CORS Support**: Configured for PropBolt domains
- **Documentation**: Swagger/OpenAPI documentation

## Configuration

### Environment Variables

The service uses the following environment variables (configured in `app.yaml`):

```yaml
# Database Configuration
DB_HOST: *************
DB_NAME: propbolt
DB_USER: propbolt_user
DB_PASSWORD: PropBolt2024!

# API Configuration
REAL_ESTATE_API_KEY: AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914
REAL_ESTATE_API_URL: https://api.realestateapi.com

# Google Cloud Configuration
GCP_PROJECT_ID: gold-braid-458901-v2
GCP_REGION: us-central1
```

### Google Cloud Endpoints

The service uses Google Cloud Endpoints for:
- API management and monitoring
- Authentication and authorization
- Rate limiting and quotas
- Request/response logging
- Custom domain mapping

## Deployment

### Prerequisites

1. **Google Cloud CLI**: Install and authenticate with `gcloud auth login`
2. **Project Access**: Ensure access to `gold-braid-458901-v2` project
3. **App Engine**: App Engine must be enabled in the project
4. **DNS Access**: Access to configure DNS records for `api1.propbolt.com`

### Deployment Steps

1. **Deploy the service**:
   ```bash
   cd api1
   ./deploy_api1.sh
   ```

2. **Configure DNS**: After deployment, configure DNS records as instructed by the script

3. **Test the deployment**:
   ```bash
   curl https://api1.propbolt.com/health
   ```

### Manual Deployment

If you prefer manual deployment:

1. **Deploy Endpoints configuration**:
   ```bash
   gcloud endpoints services deploy openapi-endpoints.yaml
   ```

2. **Update app.yaml** with the config ID from step 1

3. **Deploy App Engine service**:
   ```bash
   gcloud app deploy app.yaml --service=api1
   ```

4. **Configure domain mapping**:
   ```bash
   gcloud app domain-mappings create api1.propbolt.com --service=api1
   ```

## Usage

### Authentication

All API endpoints (except `/health` and `/`) require an API key. Include the API key in requests using:

- **Header**: `x-api-key: YOUR_API_KEY`
- **Authorization**: `Authorization: Bearer YOUR_API_KEY`
- **Query Parameter**: `?api_key=YOUR_API_KEY`

### Example Request

```bash
curl -X POST https://api1.propbolt.com/v2/PropertySearch \
  -H "x-api-key: YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "123 Main St",
    "city": "Los Angeles",
    "state": "CA",
    "size": 10
  }'
```

### Response Format

All successful responses follow this format:

```json
{
  "success": true,
  "data": {
    // API-specific response data
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Monitoring

### Health Checks

- **Health Endpoint**: `GET /health`
- **Root Endpoint**: `GET /`

### Google Cloud Monitoring

- **App Engine Console**: Monitor service performance and logs
- **Endpoints Console**: View API usage, latency, and errors
- **Cloud Logging**: Detailed application logs

### Key Metrics

- Request latency and throughput
- Error rates by endpoint
- API key usage and quotas
- Database connection health

## Development

### Local Development

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set environment variables**:
   ```bash
   export DB_HOST=*************
   export REAL_ESTATE_API_KEY=your_key_here
   # ... other variables
   ```

3. **Run locally**:
   ```bash
   python main.py
   ```

### Testing

Test endpoints locally:

```bash
# Health check
curl http://localhost:8080/health

# API endpoint (with API key)
curl -X POST http://localhost:8080/v2/PropertySearch \
  -H "x-api-key: AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914" \
  -H "Content-Type: application/json" \
  -d '{"address": "123 Main St", "city": "Los Angeles", "state": "CA"}'
```

## Support

For issues or questions:
- Check the logs in Google Cloud Console
- Review the health check endpoint
- Contact the PropBolt development team

## License

Proprietary - PropBolt Technologies

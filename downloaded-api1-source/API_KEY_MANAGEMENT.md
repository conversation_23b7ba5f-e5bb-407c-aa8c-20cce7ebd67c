# PropBolt API1 Service - API Key Management Strategy

## 🔑 **Current Implementation**

### **API Key Format**
- **Live Keys**: `pb_live_[32-character-string]` (e.g., `pb_live_abc123def456ghi789jkl012mno345`)
- **Test Keys**: `pb_test_[32-character-string]` (e.g., `pb_test_xyz789uvw456rst123opq890lmn567`)
- **Admin Fallback**: `AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914` (for testing and admin access)

### **Authentication Flow**
1. **User Request** → API1 service with PropBolt API key in header
2. **API1 Validation** → Validates key format and permissions
3. **External API Call** → Uses admin key to call RealEstateAPI.com
4. **User Tracking** → User's key included in `x-user-id` header for billing/tracking

### **Current Validation Logic**
```python
def validate_propbolt_api_key(api_key):
    """
    Current validation (simplified for development):
    - Accepts any key starting with 'pb_live_' or 'pb_test_'
    - Accepts admin fallback key for testing
    - In production: will check database for validity, limits, status
    """
    if not api_key:
        return False, "API key required"
    
    if api_key.startswith(('pb_live_', 'pb_test_')) or api_key == ADMIN_KEY:
        return True, None
    
    return False, "Invalid API key format"
```

## 🗄️ **Database Integration Plan**

### **PropBolt PostgreSQL Database Schema**
```sql
-- API Keys table
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key_value VARCHAR(64) UNIQUE NOT NULL,
    key_type VARCHAR(10) NOT NULL CHECK (key_type IN ('live', 'test')),
    user_id UUID NOT NULL REFERENCES users(id),
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    last_used_at TIMESTAMP,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    rate_limit_per_minute INTEGER DEFAULT 100,
    rate_limit_per_hour INTEGER DEFAULT 1000,
    rate_limit_per_day INTEGER DEFAULT 10000,
    monthly_quota INTEGER DEFAULT 50000,
    usage_current_month INTEGER DEFAULT 0
);

-- Usage tracking table
CREATE TABLE api_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_key_id UUID NOT NULL REFERENCES api_keys(id),
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INTEGER NOT NULL,
    response_time_ms INTEGER,
    ip_address INET,
    user_agent TEXT,
    request_size INTEGER,
    response_size INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Rate limiting tracking
CREATE TABLE rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_key_id UUID NOT NULL REFERENCES api_keys(id),
    window_start TIMESTAMP NOT NULL,
    window_type VARCHAR(10) NOT NULL CHECK (window_type IN ('minute', 'hour', 'day')),
    request_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(api_key_id, window_start, window_type)
);
```

### **Database Connection Configuration**
```python
# Environment variables for database connection
DATABASE_URL = "*************************************************/propbolt"
DATABASE_POOL_SIZE = 10
DATABASE_MAX_OVERFLOW = 20
```

## 👤 **User Registration & Key Generation Workflow**

### **1. User Registration Process**
1. User signs up at `https://propbolt.com/signup`
2. Email verification and account activation
3. User redirected to dashboard at `https://go.propbolt.com`
4. Default API keys generated automatically

### **2. API Key Generation**
```python
import secrets
import string

def generate_api_key(key_type='live'):
    """Generate a secure API key"""
    # Generate 32-character random string
    alphabet = string.ascii_letters + string.digits
    key_suffix = ''.join(secrets.choice(alphabet) for _ in range(32))
    
    # Format: pb_live_xxx or pb_test_xxx
    return f"pb_{key_type}_{key_suffix}"

def create_api_key(user_id, name, key_type='live'):
    """Create new API key for user"""
    key_value = generate_api_key(key_type)
    
    # Insert into database
    api_key = APIKey(
        key_value=key_value,
        key_type=key_type,
        user_id=user_id,
        name=name,
        rate_limit_per_minute=100,  # Default limits
        rate_limit_per_hour=1000,
        rate_limit_per_day=10000,
        monthly_quota=50000
    )
    
    return api_key
```

### **3. Key Management Dashboard**
- **View Keys**: List all API keys with usage stats
- **Create Keys**: Generate new keys with custom names
- **Rotate Keys**: Generate new key, deprecate old one
- **Revoke Keys**: Immediately disable key access
- **Usage Analytics**: Real-time usage charts and limits

## 📊 **Usage Tracking & Billing Integration**

### **Real-time Usage Tracking**
```python
def log_api_usage(api_key_id, endpoint, method, status_code, response_time_ms, ip_address, user_agent):
    """Log API usage for billing and analytics"""
    usage_record = APIUsage(
        api_key_id=api_key_id,
        endpoint=endpoint,
        method=method,
        status_code=status_code,
        response_time_ms=response_time_ms,
        ip_address=ip_address,
        user_agent=user_agent,
        created_at=datetime.utcnow()
    )
    
    # Update monthly usage counter
    update_monthly_usage(api_key_id)
    
    # Check rate limits
    check_rate_limits(api_key_id)
```

### **Billing Integration**
- **Monthly Usage Reports**: Automated billing based on API calls
- **Overage Alerts**: Notify users approaching limits
- **Usage Tiers**: Different pricing for different usage levels
- **Real-time Billing**: Stripe integration for automatic charges

## 🛡️ **Enhanced Authentication Features**

### **Rate Limiting Implementation**
```python
def check_rate_limit(api_key_id, window_type='minute'):
    """Check if API key has exceeded rate limits"""
    now = datetime.utcnow()
    
    if window_type == 'minute':
        window_start = now.replace(second=0, microsecond=0)
        limit_field = 'rate_limit_per_minute'
    elif window_type == 'hour':
        window_start = now.replace(minute=0, second=0, microsecond=0)
        limit_field = 'rate_limit_per_hour'
    elif window_type == 'day':
        window_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        limit_field = 'rate_limit_per_day'
    
    # Get or create rate limit record
    rate_limit = get_or_create_rate_limit(api_key_id, window_start, window_type)
    api_key = get_api_key(api_key_id)
    
    limit = getattr(api_key, limit_field)
    
    if rate_limit.request_count >= limit:
        return False, f"Rate limit exceeded: {limit} requests per {window_type}"
    
    # Increment counter
    rate_limit.request_count += 1
    save_rate_limit(rate_limit)
    
    return True, None
```

### **API Key Lifecycle Management**
- **Automatic Expiration**: Keys expire after set period
- **Key Rotation**: Scheduled rotation for security
- **Audit Logging**: Track all key operations
- **Suspicious Activity Detection**: Monitor for unusual usage patterns

## 🔧 **Implementation Roadmap**

### **Phase 1: Database Integration** (Current Priority)
- [ ] Connect to PropBolt PostgreSQL database
- [ ] Implement database-driven API key validation
- [ ] Add usage tracking to database
- [ ] Basic rate limiting implementation

### **Phase 2: Enhanced Features**
- [ ] Advanced rate limiting (per minute/hour/day)
- [ ] Usage quotas and billing integration
- [ ] API key management dashboard
- [ ] Real-time usage analytics

### **Phase 3: Security & Monitoring**
- [ ] Suspicious activity detection
- [ ] Automated key rotation
- [ ] Advanced audit logging
- [ ] Security alerts and notifications

## 🌐 **Production Deployment Considerations**

### **Environment Variables**
```bash
# Database
DATABASE_URL=**************************************************
DATABASE_POOL_SIZE=10

# API Keys
REAL_ESTATE_API_KEY=AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914
REAL_ESTATE_API_URL=https://api.realestateapi.com

# Security
JWT_SECRET_KEY=your-jwt-secret-key
ENCRYPTION_KEY=your-encryption-key

# Monitoring
LOG_LEVEL=INFO
SENTRY_DSN=your-sentry-dsn
```

### **Monitoring & Alerting**
- **Health Checks**: Automated monitoring of API availability
- **Performance Metrics**: Response times, error rates, throughput
- **Usage Alerts**: Notify when users approach limits
- **Security Monitoring**: Detect and alert on suspicious activity

This comprehensive API key management strategy ensures secure, scalable, and user-friendly access to the PropBolt API1 service while maintaining proper tracking, billing, and security controls.

#!/usr/bin/env python3
"""
Database Migration Script for PropBolt API Key Management
Creates the necessary tables for API key management
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

# Add the parent directory to the path so we can import our models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.models import Base, APIKey, APIKeyStatus, APIKeyTier
from database.connection import DatabaseConfig
from database.services import APIKeyService
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_tables():
    """Create all database tables"""
    config = DatabaseConfig()
    engine = create_engine(config.sync_database_url)
    
    try:
        logger.info("Creating database tables...")
        Base.metadata.create_all(engine)
        logger.info("✅ Database tables created successfully")
        return True
    except SQLAlchemyError as e:
        logger.error(f"❌ Failed to create tables: {e}")
        return False
    finally:
        engine.dispose()


def create_default_api_keys():
    """Create default API keys for testing and initial setup"""
    try:
        logger.info("Creating default API keys...")
        
        # Create a test API key
        test_key_model, test_key = APIKeyService.create_api_key(
            name="Test API Key",
            description="Default test API key for development",
            tier=APIKeyTier.FREE,
            organization="PropBolt",
            contact_email="<EMAIL>",
            daily_quota=10000,
            monthly_quota=100000,
            is_test_key=True
        )
        
        # Create a production API key
        prod_key_model, prod_key = APIKeyService.create_api_key(
            name="Production API Key",
            description="Default production API key",
            tier=APIKeyTier.PROFESSIONAL,
            organization="PropBolt",
            contact_email="<EMAIL>",
            daily_quota=50000,
            monthly_quota=1000000,
            is_test_key=False
        )
        
        logger.info("✅ Default API keys created:")
        logger.info(f"   Test Key: {test_key}")
        logger.info(f"   Production Key: {prod_key}")
        logger.info("   ⚠️  SAVE THESE KEYS - THEY WON'T BE SHOWN AGAIN!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create default API keys: {e}")
        return False


def test_database_connection():
    """Test database connection"""
    config = DatabaseConfig()
    engine = create_engine(config.sync_database_url)
    
    try:
        logger.info("Testing database connection...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.scalar() == 1:
                logger.info("✅ Database connection successful")
                return True
            else:
                logger.error("❌ Database connection test failed")
                return False
    except SQLAlchemyError as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False
    finally:
        engine.dispose()


def check_existing_tables():
    """Check if tables already exist"""
    config = DatabaseConfig()
    engine = create_engine(config.sync_database_url)
    
    try:
        with engine.connect() as conn:
            # Check if api_keys table exists
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'api_keys'
                );
            """))
            
            exists = result.scalar()
            if exists:
                logger.info("📋 API key tables already exist")
                
                # Count existing keys
                result = conn.execute(text("SELECT COUNT(*) FROM api_keys"))
                count = result.scalar()
                logger.info(f"📊 Found {count} existing API keys")
                
            return exists
            
    except SQLAlchemyError as e:
        logger.error(f"❌ Failed to check existing tables: {e}")
        return False
    finally:
        engine.dispose()


def drop_tables():
    """Drop all tables (use with caution!)"""
    config = DatabaseConfig()
    engine = create_engine(config.sync_database_url)
    
    try:
        logger.warning("⚠️  Dropping all API key management tables...")
        Base.metadata.drop_all(engine)
        logger.info("✅ Tables dropped successfully")
        return True
    except SQLAlchemyError as e:
        logger.error(f"❌ Failed to drop tables: {e}")
        return False
    finally:
        engine.dispose()


def main():
    """Main migration function"""
    print("🚀 PropBolt API Key Management Database Setup")
    print("=" * 50)
    
    # Test database connection first
    if not test_database_connection():
        print("❌ Cannot proceed without database connection")
        sys.exit(1)
    
    # Check if tables already exist
    tables_exist = check_existing_tables()
    
    if tables_exist:
        print("\n📋 Tables already exist. What would you like to do?")
        print("1. Skip table creation")
        print("2. Drop and recreate tables (⚠️  DESTRUCTIVE)")
        print("3. Exit")
        
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "2":
            confirm = input("⚠️  This will DELETE ALL existing data. Type 'DELETE' to confirm: ")
            if confirm == "DELETE":
                if not drop_tables():
                    sys.exit(1)
                tables_exist = False
            else:
                print("Operation cancelled")
                sys.exit(0)
        elif choice == "3":
            sys.exit(0)
    
    # Create tables if they don't exist
    if not tables_exist:
        if not create_tables():
            sys.exit(1)
    
    # Ask about creating default API keys
    print("\n🔑 Would you like to create default API keys?")
    print("This will create test and production keys for initial setup.")
    
    create_keys = input("Create default keys? (y/N): ").strip().lower()
    
    if create_keys in ['y', 'yes']:
        if not create_default_api_keys():
            print("⚠️  Failed to create default keys, but tables are ready")
    
    print("\n✅ Database setup complete!")
    print("\n📝 Next steps:")
    print("1. Update your app.yaml with database environment variables")
    print("2. Deploy your application")
    print("3. Test the API key authentication")
    print("\n🔗 Database connection details:")
    config = DatabaseConfig()
    print(f"   Host: {config.db_host}")
    print(f"   Port: {config.db_port}")
    print(f"   Database: {config.db_name}")


if __name__ == "__main__":
    main()

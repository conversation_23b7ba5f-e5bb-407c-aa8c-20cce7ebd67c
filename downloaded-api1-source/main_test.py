#!/usr/bin/env python3
"""
Minimal test version of PropBolt API1 Service
"""

import os
from datetime import datetime
from flask import Flask, jsonify

# Initialize Flask app
app = Flask(__name__)

@app.route('/', methods=['GET'])
def root():
    """API information"""
    return jsonify({
        'message': 'PropBolt API1 Service - Test Version',
        'version': '1.0.0-test',
        'status': 'running',
        'timestamp': datetime.utcnow().isoformat()
    })

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0-test',
        'service': 'api1.propbolt.com'
    })

@app.route('/test', methods=['GET'])
def test_endpoint():
    """Test endpoint"""
    return jsonify({
        'message': 'Test endpoint working',
        'timestamp': datetime.utcnow().isoformat(),
        'environment': {
            'PORT': os.getenv('PORT', 'not set'),
            'GAE_SERVICE': os.getenv('GAE_SERVICE', 'not set'),
            'GAE_VERSION': os.getenv('GAE_VERSION', 'not set')
        }
    })

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)

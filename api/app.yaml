runtime: go121

service: api

# ENTERPRISE AUTO-SCALING CONFIGURATION
# High-performance scaling for API service (PRIORITY #1)
automatic_scaling:
  min_instances: 2              # Always keep 2 instances running
  max_instances: 50             # Scale up to 50 instances under load
  target_cpu_utilization: 0.5  # Scale when CPU hits 50%
  target_throughput_utilization: 0.7  # Scale when throughput hits 70%
  max_concurrent_requests: 100  # Max requests per instance

# ENTERPRISE INSTANCE CONFIGURATION
instance_class: F4_1G           # High-performance instances

env_variables:
  DATABASE_URL: ************************************************************************************************************************************
  BETTER_AUTH_SECRET: POaMrdz8aecIiVY8HtH4/uGlBkRRgQYGcf7iRVWAyZU=
  REAL_ESTATE_API_KEY: AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914
  REAL_ESTATE_API_URL: https://api.realestateapi.com/v2
  GCP_PROJECT_ID: gold-braid-458901-v2
  GCP_PROJECT_NUMBER: "************"
  GCP_REGION: us-central1
  PORT: "8080"
  GIN_MODE: release
  CORS_ALLOWED_ORIGINS: https://go.propbolt.com
  GOOGLE_MAPS_API_KEY: AIzaSyBbnrFdaQisjmNJzviY9s9GrgxQvAdM6k4
  MAPTILER_API_KEY: wBfumy70aE1pm6PGKkiU
  GOOGLE_CLIENT_ID: ************-5et83k20pkgdeqsbb49nk3jf9aaf316n.apps.googleusercontent.com
  GOOGLE_CLIENT_SECRET: 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

service_account: <EMAIL>

# Enterprise resources configuration
resources:
  cpu: 2                        # High-performance CPU
  memory_gb: 4                  # Increased memory for API processing
  disk_size_gb: 20              # More storage for caching

network:
  forwarded_ports:
    - 8080

beta_settings:
  cloud_sql_instances: gold-braid-458901-v2:us-central1:propbolt-postgres

handlers:
- url: /.*
  script: auto
  secure: always
  redirect_http_response_code: 301

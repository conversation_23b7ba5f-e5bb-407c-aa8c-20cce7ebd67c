package models

import (
	"database/sql"
	"time"
)

// User represents a user in the system (BetterAuth compatible)
type User struct {
	ID            string    `json:"id" db:"id"`
	Email         string    `json:"email" db:"email"`
	EmailVerified bool      `json:"emailVerified" db:"emailVerified"`
	Name          *string   `json:"name" db:"name"`
	Image         *string   `json:"image" db:"image"`
	CreatedAt     time.Time `json:"createdAt" db:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt" db:"updatedAt"`
}

// UserRole represents user roles (admin, user, etc.)
type UserRole struct {
	ID        string    `json:"id" db:"id"`
	UserID    string    `json:"userId" db:"userId"`
	Role      string    `json:"role" db:"role"`
	CreatedAt time.Time `json:"createdAt" db:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt" db:"updatedAt"`
}

// Session represents a user session (BetterAuth compatible)
type Session struct {
	ID        string    `json:"id" db:"id"`
	ExpiresAt time.Time `json:"expiresAt" db:"expiresAt"`
	Token     string    `json:"token" db:"token"`
	CreatedAt time.Time `json:"createdAt" db:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt" db:"updatedAt"`
	IPAddress *string   `json:"ipAddress" db:"ipAddress"`
	UserAgent *string   `json:"userAgent" db:"userAgent"`
	UserID    string    `json:"userId" db:"userId"`
}

// Account represents OAuth accounts (BetterAuth compatible)
type Account struct {
	ID                     string     `json:"id" db:"id"`
	AccountID              string     `json:"accountId" db:"accountId"`
	ProviderID             string     `json:"providerId" db:"providerId"`
	UserID                 string     `json:"userId" db:"userId"`
	AccessToken            *string    `json:"accessToken" db:"accessToken"`
	RefreshToken           *string    `json:"refreshToken" db:"refreshToken"`
	IDToken                *string    `json:"idToken" db:"idToken"`
	AccessTokenExpiresAt   *time.Time `json:"accessTokenExpiresAt" db:"accessTokenExpiresAt"`
	RefreshTokenExpiresAt  *time.Time `json:"refreshTokenExpiresAt" db:"refreshTokenExpiresAt"`
	Scope                  *string    `json:"scope" db:"scope"`
	Password               *string    `json:"password" db:"password"`
	CreatedAt              time.Time  `json:"createdAt" db:"createdAt"`
	UpdatedAt              time.Time  `json:"updatedAt" db:"updatedAt"`
}

// UserWithRole represents a user with their role information
type UserWithRole struct {
	User
	Role string `json:"role"`
}

// CreateUserRequest represents the request to create a new user
type CreateUserRequest struct {
	Email    string  `json:"email" binding:"required,email"`
	Password string  `json:"password" binding:"required,min=6"`
	Name     *string `json:"name"`
}

// LoginRequest represents the login request
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse represents the login response
type LoginResponse struct {
	User    User   `json:"user"`
	Token   string `json:"token"`
	Message string `json:"message"`
}

// UserResponse represents a safe user response (without sensitive data)
type UserResponse struct {
	ID            string    `json:"id"`
	Email         string    `json:"email"`
	EmailVerified bool      `json:"emailVerified"`
	Name          *string   `json:"name"`
	Image         *string   `json:"image"`
	Role          string    `json:"role"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
}

// UpdateUserRequest represents the request to update user information
type UpdateUserRequest struct {
	Name  *string `json:"name"`
	Image *string `json:"image"`
}

// ChangePasswordRequest represents the request to change password
type ChangePasswordRequest struct {
	CurrentPassword string `json:"currentPassword" binding:"required"`
	NewPassword     string `json:"newPassword" binding:"required,min=6"`
}

// AdminUserListResponse represents the admin user list response
type AdminUserListResponse struct {
	Users      []UserResponse `json:"users"`
	Total      int            `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"pageSize"`
	TotalPages int            `json:"totalPages"`
}

// IsAdmin checks if the user has admin role
func (u *User) IsAdmin(db *sql.DB) (bool, error) {
	var role string
	err := db.QueryRow("SELECT role FROM user_role WHERE userId = $1 AND role = 'admin'", u.ID).Scan(&role)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return role == "admin", nil
}

// GetUserRole gets the user's primary role
func (u *User) GetUserRole(db *sql.DB) (string, error) {
	var role string
	err := db.QueryRow("SELECT role FROM user_role WHERE userId = $1 ORDER BY createdAt ASC LIMIT 1", u.ID).Scan(&role)
	if err != nil {
		if err == sql.ErrNoRows {
			return "user", nil // Default role
		}
		return "", err
	}
	return role, nil
}

// ToResponse converts User to UserResponse with role information
func (u *User) ToResponse(db *sql.DB) (*UserResponse, error) {
	role, err := u.GetUserRole(db)
	if err != nil {
		return nil, err
	}

	return &UserResponse{
		ID:            u.ID,
		Email:         u.Email,
		EmailVerified: u.EmailVerified,
		Name:          u.Name,
		Image:         u.Image,
		Role:          role,
		CreatedAt:     u.CreatedAt,
		UpdatedAt:     u.UpdatedAt,
	}, nil
}

package services

import (
	"database/sql"
	"fmt"
	"time"

	"propbolt/database"
	"propbolt/models"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

type UserService struct {
	db *sql.DB
}

// NewUserService creates a new user service
func NewUserService() *UserService {
	return &UserService{
		db: database.GetDB(),
	}
}

// GetDB returns the database instance
func (s *UserService) GetDB() *sql.DB {
	return s.db
}

// Create<PERSON>ser creates a new user with email and password
func (s *UserService) CreateUser(req *models.CreateUserRequest) (*models.User, error) {
	// Check if user already exists
	var existingID string
	err := s.db.QueryRow("SELECT id FROM \"user\" WHERE email = $1", req.Email).Scan(&existingID)
	if err != sql.ErrNoRows {
		if err == nil {
			return nil, fmt.Errorf("user with email %s already exists", req.Email)
		}
		return nil, fmt.Errorf("error checking existing user: %v", err)
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("error hashing password: %v", err)
	}

	// Generate IDs
	userID := uuid.New().String()
	accountID := uuid.New().String()
	roleID := uuid.New().String()

	// Begin transaction
	tx, err := s.db.Begin()
	if err != nil {
		return nil, fmt.Errorf("error starting transaction: %v", err)
	}
	defer tx.Rollback()

	// Create user
	now := time.Now()
	_, err = tx.Exec(`
		INSERT INTO "user" (id, email, "emailVerified", name, "createdAt", "updatedAt")
		VALUES ($1, $2, $3, $4, $5, $6)`,
		userID, req.Email, false, req.Name, now, now)
	if err != nil {
		return nil, fmt.Errorf("error creating user: %v", err)
	}

	// Create account for password authentication
	_, err = tx.Exec(`
		INSERT INTO account (id, "accountId", "providerId", "userId", password, "createdAt", "updatedAt")
		VALUES ($1, $2, $3, $4, $5, $6, $7)`,
		accountID, req.Email, "credential", userID, string(hashedPassword), now, now)
	if err != nil {
		return nil, fmt.Errorf("error creating account: %v", err)
	}

	// Create user role (default: user)
	_, err = tx.Exec(`
		INSERT INTO user_role (id, "userId", role, "createdAt", "updatedAt")
		VALUES ($1, $2, $3, $4, $5)`,
		roleID, userID, "user", now, now)
	if err != nil {
		return nil, fmt.Errorf("error creating user role: %v", err)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("error committing transaction: %v", err)
	}

	// Return created user
	user := &models.User{
		ID:            userID,
		Email:         req.Email,
		EmailVerified: false,
		Name:          req.Name,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	return user, nil
}

// AuthenticateUser authenticates a user with email and password
func (s *UserService) AuthenticateUser(req *models.LoginRequest) (*models.User, error) {
	// Get user and password hash
	var user models.User
	var hashedPassword string

	query := `
		SELECT u.id, u.email, u."emailVerified", u.name, u.image, u."createdAt", u."updatedAt", a.password
		FROM "user" u
		JOIN account a ON u.id = a."userId"
		WHERE u.email = $1 AND a."providerId" = 'credential'`

	err := s.db.QueryRow(query, req.Email).Scan(
		&user.ID, &user.Email, &user.EmailVerified, &user.Name, &user.Image,
		&user.CreatedAt, &user.UpdatedAt, &hashedPassword)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("invalid email or password")
		}
		return nil, fmt.Errorf("error querying user: %v", err)
	}

	// Check password
	err = bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(req.Password))
	if err != nil {
		return nil, fmt.Errorf("invalid email or password")
	}

	return &user, nil
}

// GetUserByID retrieves a user by ID
func (s *UserService) GetUserByID(userID string) (*models.User, error) {
	var user models.User

	query := `SELECT id, email, "emailVerified", name, image, "createdAt", "updatedAt" FROM "user" WHERE id = $1`
	err := s.db.QueryRow(query, userID).Scan(
		&user.ID, &user.Email, &user.EmailVerified, &user.Name, &user.Image,
		&user.CreatedAt, &user.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("error querying user: %v", err)
	}

	return &user, nil
}

// GetUserByEmail retrieves a user by email
func (s *UserService) GetUserByEmail(email string) (*models.User, error) {
	var user models.User

	query := `SELECT id, email, "emailVerified", name, image, "createdAt", "updatedAt" FROM "user" WHERE email = $1`
	err := s.db.QueryRow(query, email).Scan(
		&user.ID, &user.Email, &user.EmailVerified, &user.Name, &user.Image,
		&user.CreatedAt, &user.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("error querying user: %v", err)
	}

	return &user, nil
}

// UpdateUser updates user information
func (s *UserService) UpdateUser(userID string, req *models.UpdateUserRequest) (*models.User, error) {
	now := time.Now()

	query := `UPDATE "user" SET name = $1, image = $2, "updatedAt" = $3 WHERE id = $4`
	_, err := s.db.Exec(query, req.Name, req.Image, now, userID)
	if err != nil {
		return nil, fmt.Errorf("error updating user: %v", err)
	}

	return s.GetUserByID(userID)
}

// ChangePassword changes user password
func (s *UserService) ChangePassword(userID string, req *models.ChangePasswordRequest) error {
	// Get current password hash
	var currentHash string
	err := s.db.QueryRow(`SELECT password FROM account WHERE "userId" = $1 AND "providerId" = 'credential'`, userID).Scan(&currentHash)
	if err != nil {
		return fmt.Errorf("error getting current password: %v", err)
	}

	// Verify current password
	err = bcrypt.CompareHashAndPassword([]byte(currentHash), []byte(req.CurrentPassword))
	if err != nil {
		return fmt.Errorf("current password is incorrect")
	}

	// Hash new password
	newHash, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("error hashing new password: %v", err)
	}

	// Update password
	now := time.Now()
	_, err = s.db.Exec(`UPDATE account SET password = $1, "updatedAt" = $2 WHERE "userId" = $3 AND "providerId" = 'credential'`,
		string(newHash), now, userID)
	if err != nil {
		return fmt.Errorf("error updating password: %v", err)
	}

	return nil
}

// GetAllUsers retrieves all users (admin function)
func (s *UserService) GetAllUsers(page, pageSize int) (*models.AdminUserListResponse, error) {
	offset := (page - 1) * pageSize

	// Get total count
	var total int
	err := s.db.QueryRow(`SELECT COUNT(*) FROM "user"`).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("error counting users: %v", err)
	}

	// Get users with roles
	query := `
		SELECT u.id, u.email, u."emailVerified", u.name, u.image, u."createdAt", u."updatedAt", 
		       COALESCE(ur.role, 'user') as role
		FROM "user" u
		LEFT JOIN user_role ur ON u.id = ur."userId"
		ORDER BY u."createdAt" DESC
		LIMIT $1 OFFSET $2`

	rows, err := s.db.Query(query, pageSize, offset)
	if err != nil {
		return nil, fmt.Errorf("error querying users: %v", err)
	}
	defer rows.Close()

	var users []models.UserResponse
	for rows.Next() {
		var user models.UserResponse
		err := rows.Scan(&user.ID, &user.Email, &user.EmailVerified, &user.Name, &user.Image,
			&user.CreatedAt, &user.UpdatedAt, &user.Role)
		if err != nil {
			return nil, fmt.Errorf("error scanning user: %v", err)
		}
		users = append(users, user)
	}

	totalPages := (total + pageSize - 1) / pageSize

	return &models.AdminUserListResponse{
		Users:      users,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// SetUserRole sets a user's role (admin function)
func (s *UserService) SetUserRole(userID, role string) error {
	// Check if role exists
	var existingRoleID string
	err := s.db.QueryRow(`SELECT id FROM user_role WHERE "userId" = $1`, userID).Scan(&existingRoleID)

	now := time.Now()

	if err == sql.ErrNoRows {
		// Create new role
		roleID := uuid.New().String()
		_, err = s.db.Exec(`INSERT INTO user_role (id, "userId", role, "createdAt", "updatedAt") VALUES ($1, $2, $3, $4, $5)`,
			roleID, userID, role, now, now)
	} else if err == nil {
		// Update existing role
		_, err = s.db.Exec(`UPDATE user_role SET role = $1, "updatedAt" = $2 WHERE "userId" = $3`,
			role, now, userID)
	}

	if err != nil {
		return fmt.Errorf("error setting user role: %v", err)
	}

	return nil
}

// DeleteUser deletes a user (admin function)
func (s *UserService) DeleteUser(userID string) error {
	// Begin transaction
	tx, err := s.db.Begin()
	if err != nil {
		return fmt.Errorf("error starting transaction: %v", err)
	}
	defer tx.Rollback()

	// Delete user roles
	_, err = tx.Exec(`DELETE FROM user_role WHERE "userId" = $1`, userID)
	if err != nil {
		return fmt.Errorf("error deleting user roles: %v", err)
	}

	// Delete accounts
	_, err = tx.Exec(`DELETE FROM account WHERE "userId" = $1`, userID)
	if err != nil {
		return fmt.Errorf("error deleting accounts: %v", err)
	}

	// Delete sessions
	_, err = tx.Exec(`DELETE FROM session WHERE "userId" = $1`, userID)
	if err != nil {
		return fmt.Errorf("error deleting sessions: %v", err)
	}

	// Delete user
	_, err = tx.Exec(`DELETE FROM "user" WHERE id = $1`, userID)
	if err != nil {
		return fmt.Errorf("error deleting user: %v", err)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("error committing transaction: %v", err)
	}

	return nil
}
